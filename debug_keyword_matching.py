#!/usr/bin/env python3
"""
调试关键词匹配算法
"""

from fast_search_engine import FastSearchEngine
from single_bert_fusion_engine import <PERSON>hancedChineseNLP

def debug_keyword_matching():
    """调试关键词匹配"""
    print("🔧 Debugging Keyword Matching Algorithm")
    print("=" * 60)
    
    # 初始化NLP处理器
    nlp = EnhancedChineseNLP()
    
    # 测试不同长度的文本
    test_cases = [
        {
            'query': 'EBITDA',
            'texts': [
                'EBITDAPS d0 prevalue',  # 短文本，包含关键词
                'Mean of absolute deviation of predicted values for Q3 EBITDA (delay 0)',  # 长文本，包含关键词
                'Profitability measure type 3 based on value of annual EBITDA',  # 中等长度，包含关键词
            ]
        },
        {
            'query': 'earnings per share',
            'texts': [
                'Fourth fiscal quarter earnings per share indicator',  # 短文本，完全匹配
                'It is defined as the consensus forward 12-month earnings per share forecast divided by the price',  # 长文本，完全匹配
                'Standard deviation of estimations of earnings per share - upcoming quarter',  # 中等长度，完全匹配
            ]
        }
    ]
    
    # 初始化搜索引擎来使用其关键词匹配方法
    engine = FastSearchEngine()
    
    for case in test_cases:
        query = case['query']
        texts = case['texts']
        
        print(f"\n🔍 Query: '{query}'")
        print("-" * 40)
        
        # 提取查询关键词
        query_keywords = nlp.extract_keywords(query, top_k=10)
        print(f"Query keywords: {query_keywords}")
        
        for i, text in enumerate(texts, 1):
            # 提取文本关键词
            text_keywords = nlp.extract_keywords(text, top_k=10)
            
            # 计算关键词相似度
            keyword_score = engine._calculate_keyword_similarity(query_keywords, text_keywords)
            
            print(f"\n{i}. Text: '{text}'")
            print(f"   Length: {len(text.split())} words")
            print(f"   Text keywords: {text_keywords[:5]}")  # 只显示前5个
            print(f"   Keyword score: {keyword_score:.4f}")
            
            # 手动分析匹配情况
            query_words = set(word.lower() for word, _ in query_keywords)
            text_words = set(word.lower() for word, _ in text_keywords)
            exact_matches = query_words.intersection(text_words)
            print(f"   Exact matches: {exact_matches}")
            print(f"   Match ratio: {len(exact_matches)}/{len(query_words)} = {len(exact_matches)/len(query_words):.2%}")
    
    print("\n🎉 Debugging completed!")

if __name__ == "__main__":
    debug_keyword_matching()
