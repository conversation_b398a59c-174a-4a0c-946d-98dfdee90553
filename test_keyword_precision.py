#!/usr/bin/env python3
"""
测试关键词匹配的精确度和长度惩罚
"""

from fast_search_engine import FastSearchEngine
import time

def test_keyword_precision():
    """测试关键词匹配精确度"""
    print("🧪 Testing Keyword Matching Precision")
    print("=" * 60)
    
    # 初始化引擎
    engine = FastSearchEngine()
    engine.preload_models_fast()
    engine.load_data_and_build_index_fast('split_files/USA_1_TOP3000.csv', sample_size=2000)
    
    # 测试精确匹配 vs 长句子稀释
    test_cases = [
        {
            'query': 'earnings per share',
            'description': '测试精确短语匹配',
            'expected': '包含完整短语的短描述应该得高分'
        },
        {
            'query': 'EBITDA',
            'description': '测试单词匹配',
            'expected': '只包含EBITDA的短描述应该比长描述得分高'
        },
        {
            'query': 'revenue',
            'description': '测试单词在不同长度文本中的表现',
            'expected': '短文本中的revenue应该比长文本中的得分高'
        }
    ]
    
    for case in test_cases:
        query = case['query']
        print(f"\n🔍 Query: '{query}'")
        print(f"📝 Test: {case['description']}")
        print(f"🎯 Expected: {case['expected']}")
        print("-" * 50)
        
        results = engine.search(query, top_k=10)
        
        # 分析前5个结果的description长度和关键词分数
        print("📊 Results Analysis:")
        for i, result in enumerate(results[:5], 1):
            desc = result.description
            desc_length = len(desc.split())
            
            if hasattr(result, 'column_scores') and 'description' in result.column_scores:
                desc_scores = result.column_scores['description']
                keyword_score = desc_scores['keyword']
                semantic_score = desc_scores['semantic']
                
                # 检查是否包含查询词
                contains_query = query.lower() in desc.lower()
                query_words = query.lower().split()
                exact_matches = sum(1 for word in query_words if word in desc.lower())
                
                print(f"  {i}. Score: {keyword_score:.3f} | Length: {desc_length:2d} words | Exact: {exact_matches}/{len(query_words)} | Contains: {contains_query}")
                print(f"     Description: {desc[:80]}...")
        
        print()
    
    print("🎉 Keyword precision test completed!")

if __name__ == "__main__":
    test_keyword_precision()
