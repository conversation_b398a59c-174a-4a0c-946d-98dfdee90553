#!/usr/bin/env python3
"""
快速Web应用
使用优化的快速初始化搜索引擎，解决初始化卡住问题
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import pandas as pd
import numpy as np
import time
import uuid
import json
from typing import Dict, List, Any
import os
import threading

# 导入快速搜索引擎
from fast_search_engine import FastSearchEngine
from optimized_model_manager import CacheConfig
from dynamic_threshold_manager import SearchContext

app = Flask(__name__)
app.secret_key = 'fast_bert_fusion_search_2024'
CORS(app)

# 全局搜索引擎实例
search_engine = None
data_loaded = False

def initialize_search_engine():
    """初始化搜索引擎"""
    global search_engine, data_loaded
    
    try:
        print("🚀 Initializing Fast BERT Fusion Search Engine...")
        
        # 创建缓存配置
        cache_config = CacheConfig(
            cache_dir="cache",
            enable_cache=True,
            cache_ttl=86400  # 24小时
        )
        
        # 初始化快速搜索引擎
        search_engine = FastSearchEngine(cache_config)
        
        # 预加载模型
        print("📥 Preloading models...")
        search_engine.preload_models_fast()
        
        # 加载数据并构建索引（使用样本数据进行快速演示）
        data_file = "split_files/USA_1_TOP3000.csv"
        if os.path.exists(data_file):
            print(f"📊 Loading data and building index...")
            
            # 根据文件大小决定样本大小
            df_size = len(pd.read_csv(data_file))
            sample_size = min(2000, df_size)  # 最多使用2000条记录进行快速演示
            
            search_engine.load_data_and_build_index_fast(
                data_file, 
                sample_size=sample_size,
                batch_size=200
            )
            
            data_loaded = True
            print("✅ Fast search engine initialized successfully!")
            
        else:
            raise FileNotFoundError(f"Data file not found: {data_file}")
            
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        data_loaded = False

@app.route('/test_buttons.html')
def test_buttons():
    """测试按钮页面"""
    return open('test_buttons.html', 'r', encoding='utf-8').read()

@app.route('/simple')
def simple_index():
    """简化版主页面"""
    # 获取可用的数据文件
    data_files = []
    split_files_dir = "split_files"
    if os.path.exists(split_files_dir):
        for filename in sorted(os.listdir(split_files_dir)):
            if filename.endswith('.csv'):
                # 解析文件名
                parts = filename.replace('.csv', '').split('_')
                if len(parts) >= 3:
                    region = parts[0]
                    version = parts[1]
                    dataset = '_'.join(parts[2:])

                    # 翻译地区名称
                    region_names = {
                        'USA': '美国',
                        'EUR': '欧洲',
                        'CHN': '中国',
                        'ASI': '亚洲',
                        'GLB': '全球'
                    }

                    # 翻译数据集名称
                    dataset_names = {
                        'TOP3000': 'TOP3000 (大盘股)',
                        'TOP2500': 'TOP2500 (大中盘股)',
                        'TOP2000U': 'TOP2000U (大中盘股)',
                        'TOP1200': 'TOP1200 (大盘股)',
                        'TOP1000': 'TOP1000 (大盘股)',
                        'TOP800': 'TOP800 (大盘股)',
                        'TOP500': 'TOP500 (大盘股)',
                        'TOP400': 'TOP400 (大盘股)',
                        'TOP200': 'TOP200 (大盘股)',
                        'TOPSP500': 'TOP SP500 (标普500)',
                        'TOPDIV3000': 'TOP DIV3000 (高股息)',
                        'MINVOL1M': 'MIN VOL 1M (最小波动)',
                        'ILLIQUID_MINVOL1M': 'ILLIQUID MIN VOL 1M (非流动性最小波动)'
                    }

                    data_files.append({
                        'filename': filename,
                        'region': region_names.get(region, region),
                        'version': version,
                        'dataset': dataset,
                        'display_name': f"{region_names.get(region, region)} - {dataset_names.get(dataset, dataset)} (v{version})"
                    })

    return render_template('simple_index.html', data_files=data_files)

@app.route('/')
def index():
    """主页"""
    # 获取可用的数据文件
    data_files = []
    split_files_dir = "split_files"
    if os.path.exists(split_files_dir):
        for filename in sorted(os.listdir(split_files_dir)):
            if filename.endswith('.csv'):
                # 解析文件名
                parts = filename.replace('.csv', '').split('_')
                if len(parts) >= 3:
                    region = parts[0]
                    version = parts[1]
                    dataset = '_'.join(parts[2:])

                    # 翻译地区名称
                    region_names = {
                        'USA': '美国',
                        'EUR': '欧洲',
                        'CHN': '中国',
                        'ASI': '亚洲',
                        'GLB': '全球'
                    }

                    # 翻译数据集名称
                    dataset_names = {
                        'TOP3000': 'TOP3000 (大盘股)',
                        'TOP2500': 'TOP2500 (大中盘股)',
                        'TOP2000U': 'TOP2000U (大中盘股)',
                        'TOP1200': 'TOP1200 (大盘股)',
                        'TOP1000': 'TOP1000 (大盘股)',
                        'TOP800': 'TOP800 (大盘股)',
                        'TOP500': 'TOP500 (大盘股)',
                        'TOP400': 'TOP400 (大盘股)',
                        'TOP200': 'TOP200 (大盘股)',
                        'TOPSP500': 'TOP SP500 (标普500)',
                        'TOPDIV3000': 'TOP DIV3000 (高股息)',
                        'MINVOL1M': 'MIN VOL 1M (最小波动)',
                        'ILLIQUID_MINVOL1M': 'ILLIQUID MIN VOL 1M (非流动性最小波动)'
                    }

                    data_files.append({
                        'filename': filename,
                        'region': region_names.get(region, region),
                        'version': version,
                        'dataset': dataset,
                        'display_name': f"{region_names.get(region, region)} - {dataset_names.get(dataset, dataset)} (v{version})"
                    })

    return render_template('index.html', data_files=data_files)

@app.route('/api/load_dataset', methods=['POST'])
def api_load_dataset():
    """加载指定数据集"""
    global search_engine, data_loaded

    try:
        data = request.get_json()
        filename = data.get('filename', '').strip()

        if not filename or not filename.endswith('.csv'):
            return jsonify({
                'success': False,
                'error': 'Invalid filename'
            })

        file_path = os.path.join("split_files", filename)
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': 'File not found'
            })

        print(f"🔄 Loading new dataset: {filename}")

        # 重新初始化搜索引擎
        cache_config = CacheConfig(
            cache_dir="cache",
            enable_cache=True,
            cache_ttl=86400
        )

        search_engine = FastSearchEngine(cache_config)
        search_engine.preload_models_fast()

        # 加载新数据集
        df_size = len(pd.read_csv(file_path))
        sample_size = min(3000, df_size)  # 增加样本大小

        search_engine.load_data_and_build_index_fast(
            file_path,
            sample_size=sample_size,
            batch_size=200
        )

        data_loaded = True

        return jsonify({
            'success': True,
            'message': f'Successfully loaded {filename}',
            'total_records': df_size,
            'sample_size': sample_size
        })

    except Exception as e:
        print(f"❌ Dataset loading error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search', methods=['POST'])
def api_search():
    """搜索API"""
    try:
        if not data_loaded or not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized. Please wait for initialization to complete.'
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        category_filter = data.get('category', '').strip()
        subcategory_filter = data.get('subcategory', '').strip()
        dataset_name_filter = data.get('dataset_name', '').strip()

        if not query:
            return jsonify({
                'success': False,
                'error': 'Query cannot be empty'
            })

        # 获取或创建会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id

        # 执行快速搜索（显示所有符合条件的结果）
        start_time = time.time()
        results = search_engine.search(query, top_k=None, search_context=SearchContext.INTERACTIVE, session_id=session_id)
        search_time = time.time() - start_time

        # 应用类型筛选
        if category_filter or subcategory_filter or dataset_name_filter:
            filtered_results = []
            for result in results:
                # 检查类别筛选
                if category_filter and result.category_name != category_filter:
                    continue
                # 检查子类别筛选
                if subcategory_filter and result.subcategory_name != subcategory_filter:
                    continue
                # 检查数据源筛选
                if dataset_name_filter and result.dataset_name != dataset_name_filter:
                    continue
                filtered_results.append(result)
            results = filtered_results
            print(f"🔍 Applied filters: category={category_filter}, subcategory={subcategory_filter}, dataset={dataset_name_filter}")
            print(f"📊 Filtered results: {len(results)} from original {len(search_engine.search(query, top_k=None, search_context=SearchContext.INTERACTIVE, session_id=session_id))}")

        # 获取会话统计
        stats = search_engine.get_session_stats(session_id)

        # 不进行自动翻译，让用户选择是否翻译
        translation_time = 0.0

        # 格式化结果（不翻译版本）
        formatted_results = []
        for result in results:

            formatted_results.append({
                'id': result.id,
                'description': result.description,
                'description_translated': None,  # 初始不翻译
                'dataset_name': result.dataset_name,
                'dataset_name_translated': result.dataset_name,
                'category_name': result.category_name,
                'category_name_translated': result.category_name,
                'subcategory_name': result.subcategory_name,
                'subcategory_name_translated': result.subcategory_name,
                'overall_score': float(round(result.overall_score, 4)),
                'semantic_score': float(round(result.semantic_score, 4)),
                'keyword_score': float(round(result.keyword_score, 4)),
                'matched_columns': result.matched_columns,
                'matched_keywords': result.matched_keywords[:10],
                'region': result.region,
                'universe': result.universe,
                'delay': result.delay
            })
        
        return jsonify({
            'success': True,
            'results': formatted_results,
            'search_time': round(search_time, 3),
            'translation_time': round(translation_time, 3),
            'total_time': round(search_time + translation_time, 3),
            'total_results': len(formatted_results),
            'session_stats': stats,
            'auto_translated': False  # 标记未自动翻译
        })
        
    except Exception as e:
        print(f"❌ Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/get_filter_options', methods=['GET'])
def api_get_filter_options():
    """获取筛选选项API"""
    try:
        if not data_loaded or not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })

        # 从当前数据集中提取所有类型信息
        df = search_engine.df

        # 获取所有唯一的类别、子类别和数据源
        categories = sorted(df['category.name'].unique().tolist())
        subcategories = sorted(df['subcategory.name'].unique().tolist())
        dataset_names = sorted(df['dataset.name'].unique().tolist())

        # 构建类别-子类别映射
        category_subcategory_map = {}
        for category in categories:
            subcats = df[df['category.name'] == category]['subcategory.name'].unique().tolist()
            category_subcategory_map[category] = sorted(subcats)

        # 构建数据源-类别映射
        dataset_category_map = {}
        for dataset in dataset_names:
            cats = df[df['dataset.name'] == dataset]['category.name'].unique().tolist()
            dataset_category_map[dataset] = sorted(cats)

        return jsonify({
            'success': True,
            'categories': categories,
            'subcategories': subcategories,
            'dataset_names': dataset_names,
            'category_subcategory_map': category_subcategory_map,
            'dataset_category_map': dataset_category_map
        })

    except Exception as e:
        print(f"❌ Filter options error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/translate', methods=['POST'])
def api_translate():
    """按需翻译API"""
    try:
        if not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })

        data = request.get_json()
        texts = data.get('texts', [])

        if not texts:
            return jsonify({
                'success': False,
                'error': 'No texts to translate'
            })

        # 批量翻译
        start_time = time.time()
        translated_texts = search_engine.translator.batch_translate(texts, 'description')
        translation_time = time.time() - start_time

        return jsonify({
            'success': True,
            'translations': translated_texts,
            'translation_time': round(translation_time, 3),
            'count': len(translated_texts)
        })

    except Exception as e:
        print(f"❌ Translation error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/status')
def api_status():
    """系统状态API"""
    if search_engine:
        init_status = search_engine.get_initialization_status()
    else:
        init_status = {"status": "not_started", "progress": 0, "message": "Engine not created"}
    
    return jsonify({
        'success': True,
        'data_loaded': data_loaded,
        'engine_ready': search_engine is not None,
        'initialization_status': init_status,
        'session_id': session.get('session_id'),
        'timestamp': time.time()
    })

@app.route('/api/performance')
def api_performance():
    """性能统计API"""
    try:
        if search_engine and hasattr(search_engine, 'model_manager'):
            model_stats = search_engine.model_manager.get_performance_stats()
            cache_sizes = search_engine.model_manager.get_cache_size()
            
            return jsonify({
                'success': True,
                'performance_stats': {
                    'search_stats': {
                        'total_searches': 0,
                        'avg_search_time': 0.0,
                        'index_build_time': 0.0
                    },
                    'model_stats': model_stats,
                    'cache_sizes': cache_sizes,
                    'indexed_documents': len(search_engine.indexed_documents) if search_engine.indexed_documents else 0
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Search engine not fully initialized'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/adjust_threshold', methods=['POST'])
def api_adjust_threshold():
    """阈值调整API"""
    try:
        if not data_loaded or not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })

        data = request.get_json()
        action = data.get('action', '').strip().lower()

        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id

        # 调整阈值
        new_thresholds = search_engine.adjust_thresholds(action, session_id)

        # 获取会话统计
        stats = search_engine.get_session_stats(session_id)

        # 如果有上一次查询，重新执行搜索
        results = []
        if session_id in search_engine.user_sessions and search_engine.user_sessions[session_id]['last_query']:
            last_query = search_engine.user_sessions[session_id]['last_query']
            print(f"🔄 重新搜索: '{last_query}' with new thresholds")

            search_results = search_engine.search(last_query, top_k=None, session_id=session_id)

            # 格式化结果（不自动翻译）
            for result in search_results:
                results.append({
                    'id': result.id,
                    'description': result.description,
                    'description_translated': None,  # 不自动翻译
                    'dataset_name': result.dataset_name,
                    'dataset_name_translated': result.dataset_name,
                    'category_name': result.category_name,
                    'category_name_translated': result.category_name,
                    'subcategory_name': result.subcategory_name,
                    'subcategory_name_translated': result.subcategory_name,
                    'overall_score': round(result.overall_score, 4),
                    'semantic_score': round(result.semantic_score, 4),
                    'keyword_score': round(result.keyword_score, 4),
                    'matched_columns': result.matched_columns,
                    'matched_keywords': result.matched_keywords[:10],
                    'region': result.region,
                    'universe': result.universe,
                    'delay': result.delay
                })

        return jsonify({
            'success': True,
            'action': action,
            'results': results,
            'session_stats': stats
        })

    except Exception as e:
        print(f"❌ Threshold adjustment error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    # 在后台线程中初始化搜索引擎
    def background_init():
        initialize_search_engine()
    
    init_thread = threading.Thread(target=background_init)
    init_thread.daemon = True
    init_thread.start()
    
    # 启动Flask应用
    print("🌐 Starting Improved BERT Fusion Search Web App...")
    print("📱 Access the app at: http://localhost:5000")
    print("⚡ Features: Diversified Search + Smart Translation + Vector Caching")
    print("🎯 Using stratified sample data for better diversity (2000 records)")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True,
        use_reloader=False
    )
