#!/usr/bin/env python3
"""
快速Web应用
使用优化的快速初始化搜索引擎，解决初始化卡住问题
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import pandas as pd
import numpy as np
import time
import uuid
import json
from typing import Dict, List, Any
import os
import threading

# 导入改进的搜索引擎
from improved_search_engine import ImprovedSearchEngine
from optimized_model_manager import CacheConfig
from dynamic_threshold_manager import SearchContext

app = Flask(__name__)
app.secret_key = 'fast_bert_fusion_search_2024'
CORS(app)

# 全局搜索引擎实例
search_engine = None
data_loaded = False

def initialize_search_engine():
    """初始化搜索引擎"""
    global search_engine, data_loaded
    
    try:
        print("🚀 Initializing Fast BERT Fusion Search Engine...")
        
        # 创建缓存配置
        cache_config = CacheConfig(
            cache_dir="cache",
            enable_cache=True,
            cache_ttl=86400  # 24小时
        )
        
        # 初始化改进的搜索引擎
        search_engine = ImprovedSearchEngine(cache_config)
        
        # 预加载模型
        print("📥 Preloading models...")
        search_engine.preload_models_fast()
        
        # 加载数据并构建索引（使用样本数据进行快速演示）
        data_file = "split_files/USA_1_TOP3000.csv"
        if os.path.exists(data_file):
            print(f"📊 Loading data and building index...")
            
            # 根据文件大小决定样本大小
            df_size = len(pd.read_csv(data_file))
            sample_size = min(2000, df_size)  # 最多使用2000条记录进行快速演示
            
            search_engine.load_data_and_build_index_fast(
                data_file, 
                sample_size=sample_size,
                batch_size=200
            )
            
            data_loaded = True
            print("✅ Fast search engine initialized successfully!")
            
        else:
            raise FileNotFoundError(f"Data file not found: {data_file}")
            
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        data_loaded = False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/search', methods=['POST'])
def api_search():
    """搜索API"""
    try:
        if not data_loaded or not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized. Please wait for initialization to complete.'
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'Query cannot be empty'
            })
        
        # 获取或创建会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id
        
        # 执行搜索（增加结果数量，使用多样性排序）
        start_time = time.time()
        results = search_engine.search(query, top_k=50, search_context=SearchContext.INTERACTIVE, session_id=session_id)
        search_time = time.time() - start_time

        # 获取会话统计
        stats = search_engine.get_session_stats(session_id)

        # 格式化结果并添加智能翻译
        formatted_results = []
        for result in results:
            # 使用改进搜索引擎的翻译功能
            translations = search_engine.translate_result(result)

            formatted_results.append({
                'id': result.id,  # ID保持原样
                'description': result.description,
                'description_translated': translations['description_translated'],
                'dataset_name': result.dataset_name,
                'dataset_name_translated': translations['dataset_name_translated'],
                'category_name': result.category_name,
                'category_name_translated': translations['category_name_translated'],
                'subcategory_name': result.subcategory_name,
                'subcategory_name_translated': translations['subcategory_name_translated'],
                'region': result.region,
                'region_translated': translations['region_translated'],
                'universe': result.universe,
                'delay': result.delay,
                'delay_translated': translations['delay_translated'],
                'overall_score': round(result.overall_score, 4),
                'semantic_score': round(result.semantic_score, 4),
                'keyword_score': round(result.keyword_score, 4),
                'matched_columns': result.matched_columns,
                'matched_keywords': result.matched_keywords[:10]
            })
        
        return jsonify({
            'success': True,
            'results': formatted_results,
            'search_time': round(search_time, 3),
            'total_results': len(formatted_results),
            'session_stats': stats
        })
        
    except Exception as e:
        print(f"❌ Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/status')
def api_status():
    """系统状态API"""
    if search_engine:
        init_status = search_engine.get_initialization_status()
    else:
        init_status = {"status": "not_started", "progress": 0, "message": "Engine not created"}
    
    return jsonify({
        'success': True,
        'data_loaded': data_loaded,
        'engine_ready': search_engine is not None,
        'initialization_status': init_status,
        'session_id': session.get('session_id'),
        'timestamp': time.time()
    })

@app.route('/api/performance')
def api_performance():
    """性能统计API"""
    try:
        if search_engine and hasattr(search_engine, 'model_manager'):
            model_stats = search_engine.model_manager.get_performance_stats()
            cache_sizes = search_engine.model_manager.get_cache_size()
            
            return jsonify({
                'success': True,
                'performance_stats': {
                    'search_stats': {
                        'total_searches': 0,
                        'avg_search_time': 0.0,
                        'index_build_time': 0.0
                    },
                    'model_stats': model_stats,
                    'cache_sizes': cache_sizes,
                    'indexed_documents': len(search_engine.indexed_documents) if search_engine.indexed_documents else 0
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Search engine not fully initialized'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/adjust_threshold', methods=['POST'])
def api_adjust_threshold():
    """阈值调整API"""
    try:
        if not data_loaded or not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })

        data = request.get_json()
        action = data.get('action', '').strip().lower()

        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id

        # 调整阈值
        new_thresholds = search_engine.adjust_thresholds(action, session_id)

        # 获取会话统计
        stats = search_engine.get_session_stats(session_id)

        # 如果有上一次查询，重新执行搜索
        results = []
        if session_id in search_engine.user_sessions and search_engine.user_sessions[session_id]['last_query']:
            last_query = search_engine.user_sessions[session_id]['last_query']
            print(f"🔄 重新搜索: '{last_query}' with new thresholds")

            search_results = search_engine.search(last_query, top_k=50, session_id=session_id)

            # 格式化结果并添加智能翻译
            for result in search_results:
                translations = search_engine.translate_result(result)

                results.append({
                    'id': result.id,  # ID保持原样
                    'description': result.description,
                    'description_translated': translations['description_translated'],
                    'dataset_name': result.dataset_name,
                    'dataset_name_translated': translations['dataset_name_translated'],
                    'category_name': result.category_name,
                    'category_name_translated': translations['category_name_translated'],
                    'subcategory_name': result.subcategory_name,
                    'subcategory_name_translated': translations['subcategory_name_translated'],
                    'region': result.region,
                    'region_translated': translations['region_translated'],
                    'universe': result.universe,
                    'delay': result.delay,
                    'delay_translated': translations['delay_translated'],
                    'overall_score': round(result.overall_score, 4),
                    'semantic_score': round(result.semantic_score, 4),
                    'keyword_score': round(result.keyword_score, 4),
                    'matched_columns': result.matched_columns,
                    'matched_keywords': result.matched_keywords[:10]
                })

        return jsonify({
            'success': True,
            'action': action,
            'results': results,
            'session_stats': stats
        })

    except Exception as e:
        print(f"❌ Threshold adjustment error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    # 在后台线程中初始化搜索引擎
    def background_init():
        initialize_search_engine()
    
    init_thread = threading.Thread(target=background_init)
    init_thread.daemon = True
    init_thread.start()
    
    # 启动Flask应用
    print("🌐 Starting Improved BERT Fusion Search Web App...")
    print("📱 Access the app at: http://localhost:5000")
    print("⚡ Features: Diversified Search + Smart Translation + Vector Caching")
    print("🎯 Using stratified sample data for better diversity (2000 records)")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True,
        use_reloader=False
    )
