#!/usr/bin/env python3
"""
单模型BERT融合搜索引擎
使用单个最优模型实现特征融合检索，优化内存使用
专门强化中文支持能力
"""

import os
import time
import numpy as np
import pandas as pd
import torch
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 导入transformers模型
from sentence_transformers import SentenceTransformer

# 中文NLP工具
import jieba
import jieba.posseg as pseg
import re
from collections import defaultdict

# 科学计算
from sklearn.metrics.pairwise import cosine_similarity

@dataclass
class SearchResult:
    """搜索结果"""
    id: str
    description: str
    dataset_name: str
    category_name: str
    subcategory_name: str
    region: str
    universe: str
    delay: str
    
    # 分数信息
    overall_score: float
    semantic_score: float
    keyword_score: float
    column_scores: Dict[str, float]
    
    # 匹配信息
    matched_columns: List[str]
    matched_keywords: List[str]
    search_mode: str

class EnhancedChineseNLP:
    """增强的中文NLP处理器"""
    
    def __init__(self):
        self.financial_terms = self._load_financial_terms()
        self.stop_words = self._load_stop_words()
        self.synonym_dict = self._load_synonyms()
        
        # 初始化jieba
        self._init_jieba()
        
        # 预编译正则表达式
        self.patterns = {
            'percentage': re.compile(r'\d+\.?\d*%'),
            'currency': re.compile(r'[¥$€£]\d+\.?\d*[万亿千百十]?'),
            'time_period': re.compile(r'\d+[年月日季度]|[上下]半年|年初|年末'),
            'financial_metric': re.compile(r'(每股收益|净资产收益率|市盈率|ROE|EPS|EBITDA|营业收入|净利润)'),
            'analyst_terms': re.compile(r'(分析师|研究员|评级|目标价|买入|卖出|持有|预测|估算)')
        }
    
    def _load_financial_terms(self) -> set:
        """加载金融术语词典"""
        return {
            # 核心中文金融指标
            '每股收益', '净资产收益率', '总资产收益率', '毛利率', '净利率',
            '营业利润率', '资产负债率', '流动比率', '速动比率', '市盈率',
            '市净率', '市销率', '营业收入', '净利润', '毛利润', '现金流',
            '自由现金流', '经营现金流', '投资现金流', '筹资现金流',
            
            # 分析师相关术语
            '分析师', '研究员', '分析员', '研报', '评级', '目标价', '盈利预测',
            '一致预期', '买入', '卖出', '持有', '增持', '减持', '中性', '推荐',
            '上调', '下调', '维持', '覆盖', '首次', '重申',
            
            # 核心英文术语
            'EPS', 'ROE', 'ROA', 'PE', 'PB', 'PS', 'EBITDA', 'EBIT',
            'earnings', 'revenue', 'profit', 'cash flow', 'analyst',
            'estimate', 'forecast', 'rating', 'target price', 'buy', 'sell', 'hold',
            'upgrade', 'downgrade', 'maintain', 'initiate', 'reiterate',
            
            # 时间相关
            '年报', '半年报', '季报', '月报', '年度', '季度', '月度',
            '同比', '环比', '年初', '年末', '季末', '月末', 'YoY', 'QoQ', 'MoM',
            
            # 行业术语
            '银行', '保险', '证券', '房地产', '制造业', '科技', '医药',
            '消费', '能源', '材料', '工业', '公用事业', '电信', '金融'
        }
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        return {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都',
            '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着',
            '没有', '看', '好', '自己', '这', '那', '里', '就是', '还是',
            '为了', '还有', '可以', '这个', '那个', '什么', '怎么', '为什么',
            '因为', '所以', '但是', '然后', '如果', '虽然', '数据', '指标',
            '公司', '企业', '股票', '证券', '市场', '行业', '报告', '分析'
        }
    
    def _load_synonyms(self) -> Dict[str, List[str]]:
        """加载同义词词典"""
        return {
            '每股收益': ['EPS', '每股盈利', '每股净收益', 'earnings per share', '每股利润'],
            '净资产收益率': ['ROE', '股东权益回报率', 'return on equity', '净资产报酬率'],
            '总资产收益率': ['ROA', '资产回报率', 'return on assets', '总资产报酬率'],
            '市盈率': ['PE', 'P/E', '价格收益比', 'price earnings ratio', '市价盈利比'],
            '市净率': ['PB', 'P/B', '价格净值比', 'price book ratio', '市价净值比'],
            '营业收入': ['营收', '销售收入', '主营业务收入', 'revenue', 'sales', '收入'],
            '净利润': ['净收益', '税后利润', 'net profit', 'net income', '净盈利'],
            '分析师': ['研究员', '分析员', 'analyst', 'researcher', '证券分析师'],
            '预测': ['预期', '预估', '估算', 'forecast', 'estimate', 'projection', '展望'],
            '增长': ['增长率', '增速', '涨幅', 'growth', 'increase', '成长'],
            '现金流': ['现金流量', '资金流', 'cash flow', '现金流动'],
            'EBITDA': ['息税折旧摊销前利润', 'earnings before interest tax depreciation amortization'],
            '买入': ['buy', '推荐', 'recommend', '强烈推荐'],
            '卖出': ['sell', '减持', 'reduce', '不推荐'],
            '持有': ['hold', '中性', 'neutral', '维持']
        }
    
    def _init_jieba(self):
        """初始化jieba分词器"""
        # 添加金融术语到jieba词典
        for term in self.financial_terms:
            jieba.add_word(term, freq=3000, tag='financial')
        
        # 添加同义词
        for key, synonyms in self.synonym_dict.items():
            jieba.add_word(key, freq=2500, tag='financial')
            for synonym in synonyms:
                jieba.add_word(synonym, freq=2000, tag='financial')
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取命名实体"""
        entities = defaultdict(list)
        
        if not text:
            return dict(entities)
        
        # 使用正则表达式提取实体
        for entity_type, pattern in self.patterns.items():
            matches = pattern.findall(text)
            if matches:
                entities[entity_type].extend(matches)
        
        return dict(entities)
    
    def segment_and_pos_tag(self, text: str) -> List[Tuple[str, str]]:
        """中文分词和词性标注"""
        if not text:
            return []
        
        words_with_pos = pseg.lcut(text)
        filtered_results = []
        
        for word, pos in words_with_pos:
            word = word.strip()
            if (len(word) >= 1 and 
                not word.isspace() and
                word not in self.stop_words):
                filtered_results.append((word, pos))
        
        return filtered_results
    
    def extract_keywords(self, text: str, top_k: int = 20) -> List[Tuple[str, float]]:
        """提取关键词和权重"""
        words_with_pos = self.segment_and_pos_tag(text)
        word_weights = defaultdict(float)
        
        for word, pos in words_with_pos:
            weight = 1.0
            
            # 金融术语权重（最高优先级）
            if word in self.financial_terms:
                weight *= 8.0
            
            # 词性权重
            if pos.startswith('n'):  # 名词
                weight *= 3.5
            elif pos.startswith('v'):  # 动词
                weight *= 2.5
            elif pos.startswith('a'):  # 形容词
                weight *= 2.0
            elif pos in ['m', 'q']:  # 数词、量词
                weight *= 3.0
            
            # 词长权重
            if len(word) >= 4:
                weight *= 3.0
            elif len(word) >= 3:
                weight *= 2.5
            elif len(word) >= 2:
                weight *= 2.0
            
            # 中英文混合权重
            if re.search(r'[\u4e00-\u9fff]', word) and re.search(r'[a-zA-Z]', word):
                weight *= 2.5
            
            # 全英文专业术语权重
            if word.isupper() and len(word) >= 2:
                weight *= 2.0
            
            word_weights[word] += weight
        
        # 排序并返回top_k
        sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
        return sorted_words[:top_k]
    
    def expand_query(self, query: str) -> Dict[str, List[str]]:
        """智能查询扩展"""
        expanded = {
            'original': [query],
            'synonyms': [],
            'related_terms': [],
            'entities': []
        }
        
        # 分词
        words = [word for word, pos in self.segment_and_pos_tag(query)]
        
        # 同义词扩展
        for word in words:
            if word in self.synonym_dict:
                expanded['synonyms'].extend(self.synonym_dict[word])
            
            # 反向查找同义词
            for key, synonyms in self.synonym_dict.items():
                if word in synonyms and key not in expanded['synonyms']:
                    expanded['synonyms'].append(key)
        
        # 实体扩展
        entities = self.extract_entities(query)
        for entity_type, entity_list in entities.items():
            expanded['entities'].extend(entity_list)
        
        # 去重
        for key in expanded:
            expanded[key] = list(set(expanded[key]))
        
        return expanded

class SingleBERTFusionEngine:
    """单模型BERT融合搜索引擎"""
    
    def __init__(self, model_name: str = "paraphrase-multilingual-MiniLM-L12-v2"):
        """
        初始化单模型BERT融合引擎
        
        Args:
            model_name: 使用的模型名称，默认使用多语言MiniLM模型（内存友好）
        """
        self.model_name = model_name
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 Using device: {self.device}")
        print(f"📦 Using model: {model_name}")
        
        # 模型
        self.model = None
        
        # 中文NLP处理器
        self.chinese_nlp = EnhancedChineseNLP()
        
        # 数据存储
        self.df = None
        self.column_features = {}
        
        # 列权重
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
    
    def load_model(self):
        """加载模型"""
        print(f"📥 Loading model: {self.model_name}")
        
        try:
            self.model = SentenceTransformer(self.model_name)
            print(f"✅ Model loaded successfully!")
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            print("🔄 Trying fallback model...")
            
            # 尝试备选模型
            fallback_models = [
                "all-MiniLM-L6-v2",
                "paraphrase-MiniLM-L6-v2"
            ]
            
            for fallback in fallback_models:
                try:
                    self.model = SentenceTransformer(fallback)
                    self.model_name = fallback
                    print(f"✅ Fallback model {fallback} loaded successfully!")
                    break
                except Exception as e2:
                    print(f"❌ Fallback model {fallback} failed: {e2}")
            
            if self.model is None:
                raise Exception("All models failed to load")
    
    def encode_text(self, text: str) -> np.ndarray:
        """编码文本"""
        if self.model is None:
            raise ValueError("Model not loaded")
        
        try:
            embeddings = self.model.encode([text])
            return embeddings[0]
            
        except Exception as e:
            print(f"⚠️ Text encoding failed: {e}")
            return np.array([])
    
    def build_fusion_index(self, df: pd.DataFrame):
        """构建融合索引"""
        print(f"🔨 Building single-model fusion index for {len(df)} records...")
        
        self.df = df
        start_time = time.time()
        
        # 加载模型
        self.load_model()
        
        target_columns = list(self.column_weights.keys())
        
        # 处理每一行数据
        for idx, row in df.iterrows():
            if idx % 50 == 0:
                print(f"   Processing row {idx}/{len(df)}")
            
            # 提取列特征
            column_features = {}
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    
                    # NLP分析
                    keywords = self.chinese_nlp.extract_keywords(text_content, top_k=15)
                    entities = self.chinese_nlp.extract_entities(text_content)
                    
                    # 文本编码
                    embeddings = self.encode_text(text_content)
                    
                    column_features[col_name] = {
                        'text': text_content,
                        'keywords': keywords,
                        'entities': entities,
                        'embeddings': embeddings
                    }
            
            # 存储特征
            self.column_features[idx] = {
                'columns': column_features,
                'row_data': row.to_dict()
            }
        
        build_time = time.time() - start_time
        print(f"✅ Single-model fusion index built in {build_time:.2f}s")
    
    def calculate_semantic_similarity(self, query_embedding: np.ndarray, 
                                    doc_embedding: np.ndarray) -> float:
        """计算语义相似度"""
        if query_embedding.size == 0 or doc_embedding.size == 0:
            return 0.0
        
        try:
            similarity = cosine_similarity(
                query_embedding.reshape(1, -1), 
                doc_embedding.reshape(1, -1)
            )[0][0]
            
            return float(similarity)
            
        except Exception as e:
            print(f"⚠️ Similarity calculation failed: {e}")
            return 0.0
    
    def calculate_keyword_similarity(self, query_keywords: List[Tuple[str, float]], 
                                   doc_keywords: List[Tuple[str, float]]) -> float:
        """计算关键词相似度"""
        if not query_keywords or not doc_keywords:
            return 0.0
        
        query_dict = {word.lower(): weight for word, weight in query_keywords}
        doc_dict = {word.lower(): weight for word, weight in doc_keywords}
        
        # 计算加权相似度
        score = 0.0
        total_query_weight = sum(query_dict.values())
        
        for word, weight in query_dict.items():
            if word in doc_dict:
                # 完全匹配
                score += min(weight, doc_dict[word]) * 2.0
            else:
                # 部分匹配
                for doc_word in doc_dict:
                    if word in doc_word or doc_word in word:
                        score += weight * 0.8
                        break
                    # 同义词匹配
                    if word in self.chinese_nlp.synonym_dict:
                        if doc_word in self.chinese_nlp.synonym_dict[word]:
                            score += weight * 1.5
                            break
        
        return score / (total_query_weight + 1e-8)
    
    def bert_fusion_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """执行BERT融合搜索"""
        if not self.column_features:
            raise ValueError("Fusion index not built. Please build index first.")
        
        print(f"🔍 Single-model BERT fusion search for: '{query}'")
        start_time = time.time()
        
        # 查询预处理和扩展
        expanded_query = self.chinese_nlp.expand_query(query)
        query_keywords = self.chinese_nlp.extract_keywords(query, top_k=15)
        
        # 编码查询
        query_embedding = self.encode_text(query)
        
        # 搜索结果
        search_results = []
        
        # 遍历所有文档
        for doc_idx, doc_data in self.column_features.items():
            column_features = doc_data['columns']
            row_data = doc_data['row_data']
            
            # 计算每列的分数
            column_scores = {}
            overall_semantic_score = 0.0
            overall_keyword_score = 0.0
            overall_score = 0.0
            matched_columns = []
            matched_keywords = []
            
            for col_name, col_feature in column_features.items():
                # 关键词匹配分数
                keyword_score = self.calculate_keyword_similarity(
                    query_keywords, col_feature['keywords']
                )
                
                # 语义相似度分数
                semantic_score = self.calculate_semantic_similarity(
                    query_embedding, col_feature['embeddings']
                )
                
                # 综合列分数
                col_score = keyword_score * 0.4 + semantic_score * 0.6
                column_scores[col_name] = col_score
                
                # 累积分数
                col_weight = self.column_weights[col_name]
                overall_semantic_score += semantic_score * col_weight
                overall_keyword_score += keyword_score * col_weight
                overall_score += col_score * col_weight
                
                # 记录匹配信息
                if col_score > 0.1:
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw, weight in col_feature['keywords'][:5]])
            
            # 创建搜索结果
            if overall_score > 0.01:
                result = SearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),
                    
                    overall_score=overall_score,
                    semantic_score=overall_semantic_score,
                    keyword_score=overall_keyword_score,
                    column_scores=column_scores,
                    
                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='single_bert_fusion'
                )
                
                search_results.append(result)
        
        # 排序并返回top_k结果
        search_results.sort(key=lambda x: x.overall_score, reverse=True)
        
        search_time = time.time() - start_time
        print(f"✅ Single-model BERT fusion search completed in {search_time:.3f}s, found {len(search_results)} results")
        
        return search_results[:top_k]

if __name__ == "__main__":
    # 示例用法
    print("🤖 Single-Model BERT Fusion Search Engine")
    print("=" * 60)
    
    # 初始化引擎
    engine = SingleBERTFusionEngine()
    
    # 加载测试数据
    print("📊 Loading test data...")
    df = pd.read_csv("split_files/USA_1_TOP3000.csv").head(300)  # 测试300条数据
    
    # 构建融合索引
    engine.build_fusion_index(df)
    
    # 测试搜索
    test_queries = [
        "每股收益",
        "earnings per share", 
        "分析师预测",
        "EBITDA相关指标",
        "净资产收益率 ROE",
        "市盈率估值分析",
        "现金流量表",
        "营业收入增长"
    ]
    
    print("\n🧪 开始搜索测试...")
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = engine.bert_fusion_search(query, top_k=5)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.id} (总分: {result.overall_score:.4f})")
            print(f"   描述: {result.description[:60]}...")
            print(f"   语义分数: {result.semantic_score:.4f}, 关键词分数: {result.keyword_score:.4f}")
            print(f"   匹配列: {result.matched_columns}")
            print(f"   关键词: {result.matched_keywords[:5]}")
            print()
    
    print("🎉 测试完成！")
