import requests
from os import environ
import os
from time import sleep
import time
import json
import pandas as pd


def login():
    
    username = "<EMAIL>"
    password = "Kyz417442"
 
    # Create a session to persistently store the headers
    s = requests.Session()
 
    # Save credentials into session
    s.auth = (username, password)
 
    # Send a POST request to the /authentication API
    response = s.post('https://api.worldquantbrain.com/authentication')
    print(response.content)
    return s  

import pandas as pd

user_id = ""
s = login()
res = s.get("https://api.worldquantbrain.com/operators")
df = pd.DataFrame(res.json())[['name']]
df.to_csv(f"{user_id}_operators.csv", index=False)

s=login()

# create a function to get all operators, return is a list of dict, url is https://api.worldquantbrain.com/operators
def get_operators(s):
    response = s.get('https://api.worldquantbrain.com/operators')
    operators = response.json()
    return operators

# get_operators(s)



s=login()
len(get_operators(s))

# create a function to get details of a operator, return is json, url is https://api.worldquantbrain.com/datasetsoperators/{operator_id}. 
def get_operator_details(s, document_url):
    response = s.get(f'https://api.worldquantbrain.com{document_url}')
    operator = response.json()
    return operator



s=login()

# Get all Operators, in list of dict
list_of_operators = get_operators(s)

# convert the list of dict to a pandas dataframe
df_operators = pd.DataFrame(list_of_operators)

# loop df_operator, "documentation" column is not None, get the details of the operator, convert to a dataframe using get_operator(s, document_url), then merge with df_operators. 
# all columns in the details should be added to df_operators

# add 'details', 'lastModified', 'content' to df_operators

df_operators['details'] = None
df_operators['content'] = None
df_operators['lastModified'] = None

for index, row in df_operators.iterrows():
    if pd.notnull(row['documentation']) and row['documentation'] != 'None':
        detail_operator = get_operator_details(s, row['documentation'])
        df_operators.at[index, 'details'] = detail_operator
        df_operators.at[index, 'content'] = detail_operator.get('content')
        df_operators.at[index, 'lastModified'] = detail_operator.get('lastModified')
        

df_operators.tail()

# Save all the details of the operators to a csv file
df_operators.to_csv('operators_2025-master.csv', index=False)


# OPTIONS url = “https://api.worldquantbrain.com/simulations” 获得json格式返回值。
def options_simulations(s):
    response = s.options('https://api.worldquantbrain.com/simulations')
    return response.json()





s=login()
settings = options_simulations(s)
settings

# Function to get all combinations
def get_combinations(settings):

    config = settings['actions']['POST']['settings']['children']
    regions = config['region']['choices']['instrumentType']['EQUITY']
    delays = config['delay']['choices']['instrumentType']['EQUITY']
    universes = config['universe']['choices']['instrumentType']['EQUITY']

    combinations = []
    for region in regions:
        region_value = region['value']
        region_delays = delays['region'].get(region_value, [])
        region_universes = universes['region'].get(region_value, [])
        
        for delay in region_delays:
            for universe in region_universes:
                combinations.append({
                    'region': region_value,
                    'delay': delay['value'],
                    'universe': universe['value']
                })
    return combinations

# Get and print all combinations
all_combinations = get_combinations(settings)
for combo in all_combinations:
    print(combo)


from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import requests
@retry(
    stop=stop_after_attempt(5),  # 增加重试次数到5次
    wait=wait_exponential(multiplier=30, min=60, max=300),  # 调整等待时间
    retry=retry_if_exception_type(requests.exceptions.RequestException)  # 只在特定异常时重试
)
def get_datafields(s, instrumentType, region, delay, universe):
    timeout=180
    try:
        response = s.get(
            f'https://api.worldquantbrain.com/data-fields?instrumentType=EQUITY&region={region}&delay={delay}&universe={universe}',
            timeout=timeout
        )
        response.raise_for_status()  # Raises an HTTPError for bad responses
        datafields = response.json()
        return datafields
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        raise  # Re-raise the exception to trigger a retry


ddf=pd.DataFrame()
# loop all_combinations, get the datafields for each combination, then save to a csv file.
for combo in all_combinations:
    try:
        datafields = get_datafields(s, 'EQUITY', combo['region'], combo['delay'], combo['universe'])
        df_datafields = pd.json_normalize(datafields['results'])
        df_datafields=pd.DataFrame(df_datafields)
        ddf=pd.concat([ddf,df_datafields],axis=0)
        # save to csv
        #df_datafields.to_excel(f'datafields_{combo["region"]}_{combo["delay"]}_{combo["universe"]}.xlsx')
        print(f'datafields_{combo["region"]}_{combo["delay"]}_{combo["universe"]}.csv saved - ' + str(len(df_datafields)))
    except Exception as e:
        print(f"Failed to get datafields for {combo}: {e}")
ddf.to_csv('alldatausable.csv')

ddf.to_csv('data.csv')

import pandas as pd

from user_package.machine_lib import login

user_id = ""
s = login()
res = s.get("https://api.worldquantbrain.com/operators")
df = pd.DataFrame(res.json())[['name']]
df.to_csv(f"{user_id}_operators.csv", index=False)