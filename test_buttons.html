<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>按钮功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <h3>数据集选择测试</h3>
                <select class="form-select mb-3" id="dataset-select">
                    <option value="">选择数据集...</option>
                    <option value="EUR_1_TOP2500.csv">欧洲 - TOP2500 (大中盘股) (v1)</option>
                    <option value="CHN_1_TOP2000U.csv">中国 - TOP2000U (大中盘股) (v1)</option>
                </select>
                
                <button class="btn btn-primary" id="load-dataset-btn">
                    <i class="fas fa-database me-2"></i>加载数据集
                </button>
                
                <div id="load-result" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>搜索功能测试</h3>
                <input type="text" class="form-control mb-3" id="search-input" placeholder="输入搜索关键词">
                
                <button class="btn btn-success" id="search-btn">
                    <i class="fas fa-search me-2"></i>搜索
                </button>
                
                <div id="search-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>调试信息</h3>
                <div id="debug-info" class="bg-light p-3 rounded" style="font-family: monospace; font-size: 0.9rem;">
                    等待操作...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    
    <script>
        function log(message) {
            const debugInfo = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            console.log(message);
        }

        document.addEventListener('DOMContentLoaded', () => {
            log('✅ DOM loaded');
            
            // 测试加载数据集按钮
            const loadBtn = document.getElementById('load-dataset-btn');
            if (loadBtn) {
                log('✅ Load dataset button found');
                loadBtn.addEventListener('click', async () => {
                    log('🔄 Load dataset button clicked');
                    
                    const select = document.getElementById('dataset-select');
                    const filename = select.value;
                    
                    if (!filename) {
                        log('❌ No dataset selected');
                        document.getElementById('load-result').innerHTML = 
                            '<div class="alert alert-warning">请先选择数据集</div>';
                        return;
                    }
                    
                    log(`📁 Selected filename: ${filename}`);
                    
                    try {
                        loadBtn.disabled = true;
                        loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>加载中...';
                        
                        log('📡 Sending request to /api/load_dataset');
                        
                        const response = await fetch('/api/load_dataset', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ filename: filename })
                        });
                        
                        log(`📡 Response status: ${response.status}`);
                        log(`📡 Response ok: ${response.ok}`);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        
                        const data = await response.json();
                        log(`📊 Response data: ${JSON.stringify(data)}`);
                        
                        if (data.success) {
                            document.getElementById('load-result').innerHTML = 
                                `<div class="alert alert-success">✅ ${data.message}</div>`;
                            log('✅ Dataset loaded successfully');
                        } else {
                            document.getElementById('load-result').innerHTML = 
                                `<div class="alert alert-danger">❌ ${data.error}</div>`;
                            log(`❌ Dataset loading failed: ${data.error}`);
                        }
                        
                    } catch (error) {
                        log(`❌ Error: ${error.message}`);
                        document.getElementById('load-result').innerHTML = 
                            `<div class="alert alert-danger">❌ 网络错误: ${error.message}</div>`;
                    } finally {
                        loadBtn.disabled = false;
                        loadBtn.innerHTML = '<i class="fas fa-database me-2"></i>加载数据集';
                    }
                });
            } else {
                log('❌ Load dataset button not found');
            }
            
            // 测试搜索按钮
            const searchBtn = document.getElementById('search-btn');
            if (searchBtn) {
                log('✅ Search button found');
                searchBtn.addEventListener('click', async () => {
                    log('🔍 Search button clicked');
                    
                    const input = document.getElementById('search-input');
                    const query = input.value.trim();
                    
                    if (!query) {
                        log('❌ No search query');
                        document.getElementById('search-result').innerHTML = 
                            '<div class="alert alert-warning">请输入搜索关键词</div>';
                        return;
                    }
                    
                    log(`🔍 Search query: ${query}`);
                    
                    try {
                        searchBtn.disabled = true;
                        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>搜索中...';
                        
                        log('📡 Sending request to /api/search');
                        
                        const response = await fetch('/api/search', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ 
                                query: query,
                                category: '',
                                subcategory: '',
                                dataset_name: ''
                            })
                        });
                        
                        log(`📡 Response status: ${response.status}`);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        
                        const data = await response.json();
                        log(`📊 Search results: ${data.results ? data.results.length : 0} items`);
                        
                        if (data.success) {
                            document.getElementById('search-result').innerHTML = 
                                `<div class="alert alert-success">✅ 找到 ${data.results.length} 个结果</div>`;
                            log('✅ Search completed successfully');
                        } else {
                            document.getElementById('search-result').innerHTML = 
                                `<div class="alert alert-danger">❌ ${data.error}</div>`;
                            log(`❌ Search failed: ${data.error}`);
                        }
                        
                    } catch (error) {
                        log(`❌ Error: ${error.message}`);
                        document.getElementById('search-result').innerHTML = 
                            `<div class="alert alert-danger">❌ 网络错误: ${error.message}</div>`;
                    } finally {
                        searchBtn.disabled = false;
                        searchBtn.innerHTML = '<i class="fas fa-search me-2"></i>搜索';
                    }
                });
            } else {
                log('❌ Search button not found');
            }
        });
    </script>
</body>
</html>
