#!/usr/bin/env python3
"""
高速搜索引擎
优化搜索算法，大幅提升搜索速度
"""

import time
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import os
import pickle
import threading
from pathlib import Path
from collections import defaultdict

# 导入优化的模型管理器
from optimized_model_manager import OptimizedModelManager, CacheConfig
from single_bert_fusion_engine import SearchResult, EnhancedChineseNLP
from dynamic_threshold_manager import (
    DynamicThresholdManager, SearchContext, SearchMetrics
)
from sklearn.metrics.pairwise import cosine_similarity
from advanced_translator import AdvancedTranslator

class FastSearchEngine:
    """高速搜索引擎"""
    
    def __init__(self, cache_config: CacheConfig = None):
        """初始化高速搜索引擎"""
        print("🚀 Initializing Fast Search Engine...")
        
        # 配置
        self.cache_config = cache_config or CacheConfig()
        
        # 优化的模型管理器
        self.model_manager = OptimizedModelManager(self.cache_config)
        
        # 阈值管理器
        self.threshold_manager = DynamicThresholdManager()
        
        # 高级翻译器（只翻译description）
        self.translator = AdvancedTranslator()
        
        # 数据存储
        self.df = None
        self.indexed_documents = {}
        self.data_hash = None
        
        # 预计算的向量矩阵（用于快速相似度计算）
        self.document_vectors = None
        self.document_ids = []
        
        # 用户会话管理
        self.user_sessions = {}
        
        # 初始化状态
        self.initialization_status = {
            "status": "not_started", 
            "progress": 0, 
            "message": "",
            "current_step": "",
            "total_steps": 0,
            "completed_steps": 0
        }
        
        # 列权重
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
        
        # 当前搜索阈值（更宽松的默认值）
        self.current_thresholds = {
            'min_score': 0.01,   # 提高最小分数阈值
            'semantic': 0.2,     # 提高语义阈值
            'keyword': 0.15      # 提高关键词阈值
        }
        
        print("✅ Fast Search Engine initialized")
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """获取初始化状态"""
        return self.initialization_status.copy()
    
    def preload_models_fast(self, model_configs: Dict[str, str] = None):
        """快速预加载模型"""
        self.initialization_status.update({
            "status": "initializing",
            "progress": 10,
            "message": "预加载BERT模型...",
            "current_step": "模型加载"
        })
        
        print("📥 Fast preloading models...")
        self.model_manager.preload_models(model_configs)
        
        self.initialization_status.update({
            "progress": 30,
            "message": "模型预加载完成",
        })
        
        print("✅ Models preloaded successfully")
    
    def load_data_and_build_index_fast(self, file_path: str, 
                                      sample_size: int = None,
                                      batch_size: int = 200):
        """快速加载数据并构建索引"""
        print(f"📊 Fast loading data from {file_path}...")
        
        # 加载数据
        self.df = pd.read_csv(file_path)
        
        # 如果指定了样本大小，使用分层采样
        if sample_size and sample_size < len(self.df):
            print(f"🎯 Using stratified sample of {sample_size} records")
            
            # 按首字母分层采样，确保多样性
            grouped = self.df.groupby(self.df['id'].str[0])
            sample_dfs = []
            
            for letter, group in grouped:
                group_sample_size = max(1, int(len(group) * sample_size / len(self.df)))
                if len(group) <= group_sample_size:
                    sample_dfs.append(group)
                else:
                    sample_dfs.append(group.sample(n=group_sample_size, random_state=42))
            
            self.df = pd.concat(sample_dfs, ignore_index=True)
            print(f"📈 Stratified sampling result: {len(self.df)} records")
        
        self.data_hash = self.model_manager._get_data_hash(self.df)
        
        self.initialization_status.update({
            "progress": 40,
            "message": f"已加载 {len(self.df)} 条记录",
            "current_step": "数据加载"
        })
        
        print(f"✅ Loaded {len(self.df)} records")
        print(f"🔑 Data hash: {self.data_hash[:8]}...")
        
        # 尝试加载缓存的索引
        cached_index = self.model_manager.load_index(self.data_hash, "fast_index")
        if cached_index:
            print("📦 Loading fast index from cache...")
            self.indexed_documents = cached_index['documents']
            self.document_vectors = cached_index['vectors']
            self.document_ids = cached_index['ids']
            
            self.initialization_status.update({
                "status": "completed",
                "progress": 100,
                "message": f"快速索引已从缓存加载 ({len(self.indexed_documents)} 文档)",
                "current_step": "完成"
            })
            
            print(f"✅ Fast index loaded from cache ({len(self.indexed_documents)} documents)")
            return
        
        # 构建新的快速索引
        print("🔨 Building new fast search index...")
        self._build_fast_search_index(batch_size)
        
        # 保存索引到缓存
        cache_data = {
            'documents': self.indexed_documents,
            'vectors': self.document_vectors,
            'ids': self.document_ids
        }
        self.model_manager.save_index(cache_data, self.data_hash, "fast_index")
        
        self.initialization_status.update({
            "status": "completed",
            "progress": 100,
            "message": f"快速搜索索引构建完成 ({len(self.indexed_documents)} 文档)",
            "current_step": "完成"
        })
        
        print("✅ Fast search index built and cached successfully")
    
    def _build_fast_search_index(self, batch_size: int = 200):
        """构建快速搜索索引"""
        start_time = time.time()
        
        target_columns = list(self.column_weights.keys())
        total_rows = len(self.df)
        
        self.initialization_status.update({
            "progress": 50,
            "message": "开始构建快速索引...",
            "current_step": "索引构建",
            "total_steps": total_rows,
            "completed_steps": 0
        })
        
        print(f"📝 Building fast index for {total_rows} documents...")
        
        # 收集所有需要编码的文本
        all_texts = []
        text_to_docs = defaultdict(list)
        
        for idx, row in self.df.iterrows():
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    if text_content not in [t[0] for t in all_texts]:
                        all_texts.append((text_content, col_name))
                    text_to_docs[text_content].append((idx, col_name))
        
        print(f"📦 Batch encoding {len(all_texts)} unique texts...")
        
        # 批量编码所有文本
        texts_only = [text for text, _ in all_texts]
        all_vectors = self.model_manager.batch_encode_texts(texts_only)
        text_to_vector = dict(zip(texts_only, all_vectors))
        
        # 构建文档索引和向量矩阵
        document_vectors_list = []
        document_ids = []
        
        for idx, row in self.df.iterrows():
            if idx % 500 == 0:
                print(f"   Processing document {idx}/{total_rows}")
            
            column_features = {}
            doc_vector_sum = np.zeros(384)  # BERT向量维度
            vector_count = 0
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    
                    # 获取预编码的向量
                    vector = text_to_vector.get(text_content)
                    if vector is None:
                        continue
                    
                    # NLP分析（只对description做详细分析）
                    if col_name == 'description':
                        keywords = self.model_manager.chinese_nlp.extract_keywords(text_content, top_k=10)
                        entities = self.model_manager.chinese_nlp.extract_entities(text_content)
                    else:
                        keywords = [(text_content.lower(), 1.0)]  # 简化处理
                        entities = []
                    
                    column_features[col_name] = {
                        'text': text_content,
                        'keywords': keywords,
                        'entities': entities,
                        'vector': vector
                    }
                    
                    # 累积向量（用于文档级向量）
                    doc_vector_sum += vector * self.column_weights[col_name]
                    vector_count += 1
            
            # 创建索引文档
            if column_features and vector_count > 0:
                # 归一化文档向量
                doc_vector = doc_vector_sum / np.linalg.norm(doc_vector_sum)
                
                self.indexed_documents[idx] = {
                    'doc_id': idx,
                    'column_features': column_features,
                    'row_data': row.to_dict()
                }
                
                document_vectors_list.append(doc_vector)
                document_ids.append(idx)
            
            # 更新进度
            progress = 50 + int((idx / total_rows) * 40)
            self.initialization_status.update({
                "progress": progress,
                "message": f"已处理 {idx+1}/{total_rows} 文档",
                "completed_steps": idx+1
            })
        
        # 转换为numpy矩阵用于快速计算
        if document_vectors_list:
            self.document_vectors = np.vstack(document_vectors_list)
            self.document_ids = document_ids
            print(f"📊 Built vector matrix: {self.document_vectors.shape}")
        
        build_time = time.time() - start_time
        print(f"✅ Fast search index built in {build_time:.2f}s")
        
        self.initialization_status.update({
            "progress": 95,
            "message": "正在保存快速索引到缓存...",
            "current_step": "保存缓存"
        })
    
    def adjust_thresholds(self, action: str, session_id: str = "default"):
        """调整搜索阈值"""
        if session_id not in self.user_sessions:
            self.user_sessions[session_id] = {
                'thresholds': self.current_thresholds.copy(),
                'preference_score': 0.5,
                'last_query': '',
                'search_count': 0
            }
        
        session = self.user_sessions[session_id]
        adjustment_step = 0.02
        
        if action == "more_results":
            session['thresholds']['min_score'] = max(0.005, session['thresholds']['min_score'] - adjustment_step)
            session['thresholds']['semantic'] = max(0.1, session['thresholds']['semantic'] - adjustment_step)
            session['thresholds']['keyword'] = max(0.1, session['thresholds']['keyword'] - adjustment_step)
            session['preference_score'] = max(0.0, session['preference_score'] - 0.1)
            print(f"📉 降低阈值: min={session['thresholds']['min_score']:.3f}")
            
        elif action == "more_precise":
            session['thresholds']['min_score'] = min(0.3, session['thresholds']['min_score'] + adjustment_step)
            session['thresholds']['semantic'] = min(0.6, session['thresholds']['semantic'] + adjustment_step)
            session['thresholds']['keyword'] = min(0.6, session['thresholds']['keyword'] + adjustment_step)
            session['preference_score'] = min(1.0, session['preference_score'] + 0.1)
            print(f"📈 提高阈值: min={session['thresholds']['min_score']:.3f}")
            
        elif action == "reset":
            session['thresholds'] = self.current_thresholds.copy()
            session['preference_score'] = 0.5
            print(f"🔄 重置阈值到默认值")
        
        return session['thresholds']
    
    def get_session_stats(self, session_id: str = "default") -> Dict[str, Any]:
        """获取会话统计"""
        if session_id not in self.user_sessions:
            return {
                'current_thresholds': self.current_thresholds,
                'preference_score': 0.5,
                'threshold_trend': 'stable',
                'total_searches': 0
            }
        
        session = self.user_sessions[session_id]
        return {
            'current_thresholds': session['thresholds'],
            'preference_score': session['preference_score'],
            'threshold_trend': 'stable',
            'total_searches': session['search_count']
        }

    def search(self, query: str, top_k: int = None,
               search_context: SearchContext = SearchContext.INTERACTIVE,
               session_id: str = "default") -> List[SearchResult]:
        """执行快速搜索"""
        if not self.indexed_documents or self.document_vectors is None:
            raise ValueError("Fast search index not built. Please initialize first.")

        print(f"🔍 Fast searching for: '{query}'")
        start_time = time.time()

        # 获取用户会话阈值
        if session_id not in self.user_sessions:
            self.user_sessions[session_id] = {
                'thresholds': self.current_thresholds.copy(),
                'preference_score': 0.5,
                'last_query': query,
                'search_count': 0
            }

        session = self.user_sessions[session_id]
        session['last_query'] = query
        session['search_count'] += 1

        user_thresholds = session['thresholds']

        print(f"🎛️ 使用阈值: min={user_thresholds['min_score']:.3f}, semantic={user_thresholds['semantic']:.3f}")

        # 编码查询向量
        query_vector = self.model_manager.encode_text_cached(query)
        query_keywords = self.model_manager.chinese_nlp.extract_keywords(query, top_k=10)

        # 快速向量相似度计算
        vector_start = time.time()
        similarities = cosine_similarity(
            query_vector.reshape(1, -1),
            self.document_vectors
        )[0].astype(float)  # 转换为Python float
        vector_time = time.time() - vector_start
        print(f"⚡ Vector similarity computed in {vector_time:.3f}s")

        # 筛选候选文档（基于向量相似度预筛选）
        candidate_indices = np.where(similarities > user_thresholds['semantic'] * 0.7)[0]
        print(f"📋 Pre-filtered to {len(candidate_indices)} candidates")

        # 详细评分候选文档
        search_results = []
        detail_start = time.time()

        for i, candidate_idx in enumerate(candidate_indices):
            doc_idx = self.document_ids[candidate_idx]
            indexed_doc = self.indexed_documents[doc_idx]

            column_features = indexed_doc['column_features']
            row_data = indexed_doc['row_data']

            # 计算详细分数 - 新的评分机制
            column_scores = {}
            field_max_scores = []
            matched_columns = []
            matched_keywords = []

            # 分别计算每个字段的语义分数和关键词分数
            for col_name, col_feature in column_features.items():
                # 计算该字段的语义相似度
                field_semantic_score = float(cosine_similarity(
                    query_vector.reshape(1, -1),
                    col_feature['vector'].reshape(1, -1)
                )[0][0])

                # 计算该字段的关键词匹配分数
                field_keyword_score = self._calculate_keyword_similarity(
                    query_keywords, col_feature['keywords']
                )

                # 该字段的最终分数 = max(语义分数, 关键词分数)
                field_final_score = max(field_semantic_score, field_keyword_score)

                # 记录字段分数（转换为Python float）
                column_scores[col_name] = {
                    'semantic': float(field_semantic_score),
                    'keyword': float(field_keyword_score),
                    'final': float(field_final_score)
                }

                # 收集所有字段的最高分数
                field_max_scores.append(field_final_score)

                # 记录匹配信息（如果该字段分数足够高）
                if field_final_score > user_thresholds['min_score']:
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw, weight in col_feature['keywords'][:3]])

            # 文档的最终分数 = 所有字段中的最高分（转换为Python float）
            overall_score = float(max(field_max_scores)) if field_max_scores else 0.0

            # 整体的语义和关键词分数（不取最大值，而是加权平均或其他方式）
            # 这里我们可以用加权平均或者最大值，根据需求调整
            if column_scores:
                # 计算所有字段的语义分数加权平均
                semantic_scores = [scores['semantic'] for scores in column_scores.values()]
                keyword_scores = [scores['keyword'] for scores in column_scores.values()]

                # 使用加权平均（按字段权重）
                overall_semantic_score = 0.0
                overall_keyword_score = 0.0
                total_weight = 0.0

                for col_name, scores in column_scores.items():
                    weight = self.column_weights.get(col_name, 0.1)
                    overall_semantic_score += scores['semantic'] * weight
                    overall_keyword_score += scores['keyword'] * weight
                    total_weight += weight

                if total_weight > 0:
                    overall_semantic_score = float(overall_semantic_score / total_weight)
                    overall_keyword_score = float(overall_keyword_score / total_weight)
                else:
                    overall_semantic_score = 0.0
                    overall_keyword_score = 0.0
            else:
                overall_semantic_score = 0.0
                overall_keyword_score = 0.0

            # 创建搜索结果
            if (overall_score > user_thresholds['min_score'] and
                (overall_semantic_score > user_thresholds['semantic'] or
                 overall_keyword_score > user_thresholds['keyword'])):

                result = SearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),

                    overall_score=overall_score,
                    semantic_score=overall_semantic_score,
                    keyword_score=overall_keyword_score,
                    column_scores=column_scores,  # 传入详细的字段分数

                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='fast_bert_fusion_max'  # 标记新的评分模式
                )

                search_results.append(result)

        detail_time = time.time() - detail_start
        print(f"📊 Detailed scoring in {detail_time:.3f}s")

        # 排序结果
        search_results.sort(key=lambda x: x.overall_score, reverse=True)

        # 应用top_k限制
        if top_k:
            search_results = search_results[:top_k]

        search_time = time.time() - start_time
        print(f"✅ Fast search completed in {search_time:.3f}s, found {len(search_results)} results")

        return search_results

    def _calculate_keyword_similarity(self, query_keywords: List[Tuple[str, float]],
                                    doc_keywords: List[Tuple[str, float]]) -> float:
        """计算关键词相似度（优化版）"""
        if not query_keywords or not doc_keywords:
            return 0.0

        query_dict = {word.lower(): weight for word, weight in query_keywords}
        doc_dict = {word.lower(): weight for word, weight in doc_keywords}

        score = 0.0
        total_query_weight = sum(query_dict.values())

        for word, weight in query_dict.items():
            if word in doc_dict:
                score += min(weight, doc_dict[word]) * 2.0
            else:
                # 简化部分匹配逻辑以提高速度
                for doc_word in list(doc_dict.keys())[:5]:  # 只检查前5个
                    if word in doc_word or doc_word in word:
                        score += weight * 0.6
                        break

        return score / (total_query_weight + 1e-8)

    def translate_result(self, result: SearchResult) -> Dict[str, str]:
        """翻译搜索结果（只翻译description）"""
        return {
            'id': result.id,  # ID不翻译
            'description_translated': self.translator.translate_to_chinese(result.description, 'description'),
            'dataset_name_translated': result.dataset_name,  # 不翻译
            'category_name_translated': result.category_name,  # 不翻译
            'subcategory_name_translated': result.subcategory_name,  # 不翻译
            'region_translated': result.region,  # 不翻译
            'delay_translated': result.delay  # 不翻译
        }

if __name__ == "__main__":
    # 测试快速搜索引擎
    print("🧪 Testing Fast Search Engine")
    print("=" * 60)

    # 初始化引擎
    engine = FastSearchEngine()

    # 预加载模型
    engine.preload_models_fast()

    # 快速加载数据
    engine.load_data_and_build_index_fast(
        "split_files/USA_1_TOP3000.csv",
        sample_size=2000,
        batch_size=200
    )

    # 测试搜索速度
    test_queries = [
        "earnings per share",
        "revenue growth",
        "analyst estimate"
    ]

    print(f"\n🔍 Testing fast search performance...")

    for query in test_queries:
        print(f"\n📝 Query: '{query}'")

        start_time = time.time()
        results = engine.search(query, top_k=50)
        search_time = time.time() - start_time

        print(f"   ⏱️  Search time: {search_time:.3f}s")
        print(f"   📊 Results: {len(results)}")

        for i, result in enumerate(results[:3], 1):
            print(f"   {i}. {result.id} (Score: {result.overall_score:.3f})")

    print(f"\n🎉 Fast search test completed!")
