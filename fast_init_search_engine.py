#!/usr/bin/env python3
"""
快速初始化搜索引擎
解决初始化卡住问题，实现分批处理和进度显示
"""

import time
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import os
import pickle
import threading
from pathlib import Path

# 导入优化的模型管理器
from optimized_model_manager import OptimizedModelManager, CacheConfig
from single_bert_fusion_engine import SearchResult, EnhancedChineseNLP
from dynamic_threshold_manager import (
    DynamicThresholdManager, SearchContext, SearchMetrics
)
from sklearn.metrics.pairwise import cosine_similarity

class FastInitSearchEngine:
    """快速初始化搜索引擎"""
    
    def __init__(self, cache_config: CacheConfig = None):
        """初始化快速搜索引擎"""
        print("🚀 Initializing Fast Search Engine...")
        
        # 配置
        self.cache_config = cache_config or CacheConfig()
        
        # 优化的模型管理器
        self.model_manager = OptimizedModelManager(self.cache_config)
        
        # 阈值管理器
        self.threshold_manager = DynamicThresholdManager()
        
        # 数据存储
        self.df = None
        self.indexed_documents = {}
        self.data_hash = None
        
        # 初始化状态
        self.initialization_status = {
            "status": "not_started", 
            "progress": 0, 
            "message": "",
            "current_step": "",
            "total_steps": 0,
            "completed_steps": 0
        }
        
        # 列权重
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
        
        print("✅ Fast Search Engine initialized")
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """获取初始化状态"""
        return self.initialization_status.copy()
    
    def preload_models_fast(self, model_configs: Dict[str, str] = None):
        """快速预加载模型"""
        self.initialization_status.update({
            "status": "initializing",
            "progress": 10,
            "message": "预加载BERT模型...",
            "current_step": "模型加载"
        })
        
        print("📥 Fast preloading models...")
        self.model_manager.preload_models(model_configs)
        
        self.initialization_status.update({
            "progress": 30,
            "message": "模型预加载完成",
        })
        
        print("✅ Models preloaded successfully")
    
    def load_data_and_build_index_fast(self, file_path: str, 
                                      sample_size: int = None,
                                      batch_size: int = 100):
        """快速加载数据并构建索引"""
        print(f"📊 Fast loading data from {file_path}...")
        
        # 加载数据
        self.df = pd.read_csv(file_path)
        
        # 如果指定了样本大小，只使用部分数据进行快速演示
        if sample_size and sample_size < len(self.df):
            print(f"🎯 Using sample of {sample_size} records for fast initialization")
            self.df = self.df.head(sample_size)
        
        self.data_hash = self.model_manager._get_data_hash(self.df)
        
        self.initialization_status.update({
            "progress": 40,
            "message": f"已加载 {len(self.df)} 条记录",
            "current_step": "数据加载"
        })
        
        print(f"✅ Loaded {len(self.df)} records")
        print(f"🔑 Data hash: {self.data_hash[:8]}...")
        
        # 尝试加载缓存的索引
        cached_index = self.model_manager.load_index(self.data_hash)
        if cached_index:
            print("📦 Loading index from cache...")
            self.indexed_documents = cached_index
            
            self.initialization_status.update({
                "status": "completed",
                "progress": 100,
                "message": f"索引已从缓存加载 ({len(self.indexed_documents)} 文档)",
                "current_step": "完成"
            })
            
            print(f"✅ Index loaded from cache ({len(self.indexed_documents)} documents)")
            return
        
        # 构建新索引（分批处理）
        print("🔨 Building new search index with batch processing...")
        self._build_search_index_batched(batch_size)
        
        # 保存索引到缓存
        self.model_manager.save_index(self.indexed_documents, self.data_hash)
        
        self.initialization_status.update({
            "status": "completed",
            "progress": 100,
            "message": f"搜索索引构建完成 ({len(self.indexed_documents)} 文档)",
            "current_step": "完成"
        })
        
        print("✅ Search index built and cached successfully")
    
    def _build_search_index_batched(self, batch_size: int = 100):
        """分批构建搜索索引"""
        start_time = time.time()
        
        target_columns = list(self.column_weights.keys())
        total_rows = len(self.df)
        
        self.initialization_status.update({
            "progress": 50,
            "message": "开始构建搜索索引...",
            "current_step": "索引构建",
            "total_steps": total_rows,
            "completed_steps": 0
        })
        
        print(f"📝 Building index for {total_rows} documents in batches of {batch_size}...")
        
        # 分批处理文档
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_df = self.df.iloc[batch_start:batch_end]
            
            print(f"   Processing batch {batch_start//batch_size + 1}/{(total_rows-1)//batch_size + 1} "
                  f"(rows {batch_start}-{batch_end-1})")
            
            # 收集当前批次的所有文本
            batch_texts = []
            batch_text_to_doc_col = {}
            
            for idx, row in batch_df.iterrows():
                for col_name in target_columns:
                    if col_name in row and pd.notna(row[col_name]):
                        text_content = str(row[col_name])
                        if text_content not in batch_text_to_doc_col:  # 避免重复文本
                            batch_texts.append(text_content)
                            batch_text_to_doc_col[text_content] = []
                        batch_text_to_doc_col[text_content].append((idx, col_name))
            
            # 批量编码当前批次的文本
            if batch_texts:
                print(f"     Encoding {len(batch_texts)} unique texts...")
                batch_vectors = self.model_manager.batch_encode_texts(batch_texts)
                
                # 创建文本到向量的映射
                text_to_vector = dict(zip(batch_texts, batch_vectors))
            
            # 构建当前批次的文档索引
            for idx, row in batch_df.iterrows():
                column_features = {}
                
                for col_name in target_columns:
                    if col_name in row and pd.notna(row[col_name]):
                        text_content = str(row[col_name])
                        
                        # 获取预编码的向量
                        vector = text_to_vector.get(text_content)
                        if vector is None:
                            continue
                        
                        # NLP分析
                        keywords = self.model_manager.chinese_nlp.extract_keywords(text_content, top_k=15)
                        entities = self.model_manager.chinese_nlp.extract_entities(text_content)
                        
                        column_features[col_name] = {
                            'text': text_content,
                            'keywords': keywords,
                            'entities': entities,
                            'vector': vector
                        }
                
                # 创建索引文档
                if column_features:  # 只有当有特征时才创建
                    self.indexed_documents[idx] = {
                        'doc_id': idx,
                        'column_features': column_features,
                        'row_data': row.to_dict()
                    }
            
            # 更新进度
            completed_steps = batch_end
            progress = 50 + int((completed_steps / total_rows) * 40)  # 50-90%
            
            self.initialization_status.update({
                "progress": progress,
                "message": f"已处理 {completed_steps}/{total_rows} 文档",
                "completed_steps": completed_steps
            })
        
        build_time = time.time() - start_time
        print(f"✅ Search index built in {build_time:.2f}s")
        
        self.initialization_status.update({
            "progress": 95,
            "message": "正在保存索引到缓存...",
            "current_step": "保存缓存"
        })
    
    def search(self, query: str, top_k: int = 10, 
               search_context: SearchContext = SearchContext.INTERACTIVE) -> List[SearchResult]:
        """执行搜索"""
        if not self.indexed_documents:
            raise ValueError("Search index not built. Please initialize first.")
        
        print(f"🔍 Searching for: '{query}'")
        start_time = time.time()
        
        # 获取动态阈值
        adaptive_thresholds = self.threshold_manager.get_adaptive_thresholds(
            query, search_context
        )
        query_type = self.threshold_manager.classify_query_type(query)
        
        # 查询预处理
        query_keywords = self.model_manager.chinese_nlp.extract_keywords(query, top_k=15)
        
        # 编码查询（使用缓存）
        query_vector = self.model_manager.encode_text_cached(query)
        
        # 搜索所有文档
        search_results = []
        
        for doc_idx, indexed_doc in self.indexed_documents.items():
            column_features = indexed_doc['column_features']
            row_data = indexed_doc['row_data']
            
            # 计算分数
            overall_semantic_score = 0.0
            overall_keyword_score = 0.0
            overall_score = 0.0
            matched_columns = []
            matched_keywords = []
            
            for col_name, col_feature in column_features.items():
                # 关键词匹配分数
                keyword_score = self._calculate_keyword_similarity(
                    query_keywords, col_feature['keywords']
                )
                
                # 语义相似度分数
                semantic_score = self._calculate_semantic_similarity(
                    query_vector, col_feature['vector']
                )
                
                # 综合列分数
                col_score = keyword_score * 0.4 + semantic_score * 0.6
                
                # 累积分数
                col_weight = self.column_weights[col_name]
                overall_semantic_score += semantic_score * col_weight
                overall_keyword_score += keyword_score * col_weight
                overall_score += col_score * col_weight
                
                # 记录匹配信息
                if col_score > adaptive_thresholds.min_score_threshold:
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw, weight in col_feature['keywords'][:5]])
            
            # 创建搜索结果
            if overall_score > adaptive_thresholds.min_score_threshold:
                result = SearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),
                    
                    overall_score=overall_score,
                    semantic_score=overall_semantic_score,
                    keyword_score=overall_keyword_score,
                    column_scores={},
                    
                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='fast_bert_fusion'
                )
                
                search_results.append(result)
        
        # 排序并返回结果
        search_results.sort(key=lambda x: x.overall_score, reverse=True)
        final_results = search_results[:top_k]
        
        search_time = time.time() - start_time
        print(f"✅ Search completed in {search_time:.3f}s, found {len(final_results)} results")
        
        return final_results
    
    def _calculate_semantic_similarity(self, query_vector: np.ndarray, 
                                     doc_vector: np.ndarray) -> float:
        """计算语义相似度"""
        try:
            similarity = cosine_similarity(
                query_vector.reshape(1, -1), 
                doc_vector.reshape(1, -1)
            )[0][0]
            return float(similarity)
        except:
            return 0.0
    
    def _calculate_keyword_similarity(self, query_keywords: List[Tuple[str, float]], 
                                    doc_keywords: List[Tuple[str, float]]) -> float:
        """计算关键词相似度"""
        if not query_keywords or not doc_keywords:
            return 0.0
        
        query_dict = {word.lower(): weight for word, weight in query_keywords}
        doc_dict = {word.lower(): weight for word, weight in doc_keywords}
        
        score = 0.0
        total_query_weight = sum(query_dict.values())
        
        for word, weight in query_dict.items():
            if word in doc_dict:
                score += min(weight, doc_dict[word]) * 2.0
            else:
                # 部分匹配
                for doc_word in doc_dict:
                    if word in doc_word or doc_word in word:
                        score += weight * 0.8
                        break
        
        return score / (total_query_weight + 1e-8)

if __name__ == "__main__":
    # 测试快速初始化搜索引擎
    print("🧪 Testing Fast Initialization Search Engine")
    print("=" * 60)
    
    # 初始化引擎
    engine = FastInitSearchEngine()
    
    # 预加载模型
    engine.preload_models_fast()
    
    # 快速加载数据（使用样本）
    engine.load_data_and_build_index_fast(
        "split_files/USA_1_TOP3000.csv", 
        sample_size=1000,  # 只使用1000条记录进行快速演示
        batch_size=100
    )
    
    # 测试搜索
    test_queries = [
        "每股收益",
        "earnings per share", 
        "分析师预测"
    ]
    
    print(f"\n🔍 Testing search performance...")
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        
        start_time = time.time()
        results = engine.search(query, top_k=5)
        search_time = time.time() - start_time
        
        print(f"   ⏱️  Search time: {search_time:.3f}s")
        print(f"   📊 Results: {len(results)}")
        
        for i, result in enumerate(results[:3], 1):
            print(f"   {i}. {result.id} (Score: {result.overall_score:.3f})")
    
    print(f"\n🎉 Fast initialization test completed!")
