/**
 * 简化版应用 - 用于调试按钮失灵问题
 */

console.log('📝 Simple app script loaded');

document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM loaded - Simple version');
    
    try {
        // 检查关键元素
        const searchBtn = document.getElementById('search-btn');
        const loadBtn = document.getElementById('load-dataset-btn');
        const searchInput = document.getElementById('search-input');
        const datasetSelect = document.getElementById('dataset-select');
        
        console.log('🔍 Element check (Simple):');
        console.log('  Search button:', searchBtn ? '✅ Found' : '❌ Missing');
        console.log('  Load button:', loadBtn ? '✅ Found' : '❌ Missing');
        console.log('  Search input:', searchInput ? '✅ Found' : '❌ Missing');
        console.log('  Dataset select:', datasetSelect ? '✅ Found' : '❌ Missing');
        
        // 绑定搜索按钮
        if (searchBtn) {
            searchBtn.addEventListener('click', async () => {
                console.log('🔍 Search button clicked (Simple)');
                
                const query = searchInput ? searchInput.value.trim() : '';
                if (!query) {
                    alert('请输入搜索关键词');
                    return;
                }
                
                try {
                    searchBtn.disabled = true;
                    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>搜索中...';
                    
                    const response = await fetch('/api/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            query: query,
                            category: '',
                            subcategory: '',
                            dataset_name: ''
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert(`搜索成功！找到 ${data.results.length} 个结果`);
                        console.log('✅ Search successful:', data.results.length, 'results');
                    } else {
                        alert(`搜索失败：${data.error}`);
                        console.error('❌ Search failed:', data.error);
                    }
                    
                } catch (error) {
                    alert(`网络错误：${error.message}`);
                    console.error('❌ Search error:', error);
                } finally {
                    searchBtn.disabled = false;
                    searchBtn.innerHTML = '<i class="fas fa-search me-1"></i>搜索';
                }
            });
            console.log('✅ Search button event bound (Simple)');
        }
        
        // 绑定加载数据集按钮
        if (loadBtn) {
            loadBtn.addEventListener('click', async () => {
                console.log('📊 Load dataset button clicked (Simple)');
                
                const filename = datasetSelect ? datasetSelect.value : '';
                if (!filename) {
                    alert('请先选择数据集');
                    return;
                }
                
                try {
                    loadBtn.disabled = true;
                    loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
                    
                    const response = await fetch('/api/load_dataset', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ filename: filename })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert(`数据集加载成功！${data.message}`);
                        console.log('✅ Dataset loaded successfully:', data);
                    } else {
                        alert(`数据集加载失败：${data.error}`);
                        console.error('❌ Dataset loading failed:', data.error);
                    }
                    
                } catch (error) {
                    alert(`网络错误：${error.message}`);
                    console.error('❌ Dataset loading error:', error);
                } finally {
                    loadBtn.disabled = false;
                    loadBtn.innerHTML = '<i class="fas fa-upload me-1"></i>加载数据集';
                }
            });
            console.log('✅ Load dataset button event bound (Simple)');
        }
        
        // 绑定回车键搜索
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('⌨️ Enter key pressed (Simple)');
                    if (searchBtn) {
                        searchBtn.click();
                    }
                }
            });
            console.log('✅ Enter key event bound (Simple)');
        }
        
        console.log('✅ Simple app initialized successfully');
        
    } catch (error) {
        console.error('❌ Simple app initialization failed:', error);
        console.error('❌ Error stack:', error.stack);
        alert(`应用初始化失败：${error.message}`);
    }
});

console.log('📝 Simple app script end');
