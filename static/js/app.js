// BERT融合搜索引擎 - 前端交互逻辑

class BERTSearchApp {
    constructor() {
        this.currentQuery = '';
        this.isSearching = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkSystemStatus();
        this.showWelcomeMessage();
    }

    bindEvents() {
        // 搜索按钮事件
        document.getElementById('search-btn').addEventListener('click', () => {
            this.performSearch();
        });

        // 搜索输入框回车事件
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 快速查询按钮事件
        document.querySelectorAll('.quick-query').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const query = e.target.getAttribute('data-query');
                document.getElementById('search-input').value = query;
                this.performSearch();
            });
        });

        // 阈值调整按钮事件
        document.getElementById('more-results-btn').addEventListener('click', () => {
            this.adjustThreshold('more_results');
        });

        document.getElementById('more-precise-btn').addEventListener('click', () => {
            this.adjustThreshold('more_precise');
        });

        document.getElementById('reset-btn').addEventListener('click', () => {
            this.adjustThreshold('reset');
        });
    }

    async checkSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.success && data.data_loaded) {
                this.updateStatusIndicator('ready', '系统就绪');
            } else {
                this.updateStatusIndicator('error', '系统未就绪');
            }
        } catch (error) {
            console.error('Status check failed:', error);
            this.updateStatusIndicator('error', '连接失败');
        }
    }

    updateStatusIndicator(status, message) {
        const indicator = document.getElementById('status-indicator');
        const iconClass = status === 'ready' ? 'text-success' : 
                         status === 'loading' ? 'text-warning' : 'text-danger';
        
        indicator.innerHTML = `
            <i class="fas fa-circle ${iconClass} me-1"></i>
            ${message}
        `;
    }

    showWelcomeMessage() {
        const resultsContainer = document.getElementById('search-results');
        resultsContainer.innerHTML = `
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-robot text-primary mb-3" style="font-size: 4rem;"></i>
                    <h4 class="card-title">欢迎使用BERT融合搜索引擎</h4>
                    <p class="card-text text-muted">
                        基于深度学习的智能检索系统，支持中英文混合搜索和动态阈值调整
                    </p>
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <i class="fas fa-brain text-info mb-2" style="font-size: 2rem;"></i>
                            <h6>BERT多模型融合</h6>
                            <small class="text-muted">使用多个预训练模型进行特征融合</small>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-sliders-h text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>动态阈值调整</h6>
                            <small class="text-muted">实时调整搜索精度和召回率</small>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-language text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>中英文支持</h6>
                            <small class="text-muted">支持中英文混合查询和语义理解</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async performSearch() {
        if (this.isSearching) return;

        const query = document.getElementById('search-input').value.trim();
        if (!query) {
            this.showError('请输入搜索查询');
            return;
        }

        this.currentQuery = query;
        this.isSearching = true;
        this.showLoading();
        this.updateStatusIndicator('loading', '搜索中...');

        try {
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
                this.updateThresholdStatus(data.session_stats);
                this.updateSearchStats(data);
            } else {
                this.showError(data.error || '搜索失败');
            }
        } catch (error) {
            console.error('Search failed:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateStatusIndicator('ready', '系统就绪');
        }
    }

    async adjustThreshold(action) {
        if (this.isSearching) return;

        this.isSearching = true;
        this.showLoading();
        this.updateStatusIndicator('loading', '调整阈值中...');

        try {
            const response = await fetch('/api/adjust_threshold', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: action })
            });

            const data = await response.json();

            if (data.success) {
                this.updateThresholdStatus(data.session_stats);
                
                if (data.results && data.results.length > 0) {
                    // 如果有重新搜索的结果，显示它们
                    this.displayResults({
                        results: data.results,
                        search_time: 0,
                        total_results: data.results.length
                    });
                }

                // 显示调整提示
                this.showAdjustmentMessage(action);
            } else {
                this.showError(data.error || '阈值调整失败');
            }
        } catch (error) {
            console.error('Threshold adjustment failed:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateStatusIndicator('ready', '系统就绪');
        }
    }

    showAdjustmentMessage(action) {
        const messages = {
            'more_results': '已降低阈值，获得更多结果',
            'more_precise': '已提高阈值，获得更精准结果',
            'reset': '已重置阈值到默认值'
        };

        const message = messages[action] || '阈值已调整';
        
        // 创建临时提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alert, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }

    displayResults(data) {
        const resultsContainer = document.getElementById('search-results');
        
        if (!data.results || data.results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h5>未找到匹配结果</h5>
                    <p class="text-muted">尝试使用不同的关键词，或点击"更多结果"降低搜索阈值</p>
                </div>
            `;
            return;
        }

        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list-ul me-2 text-primary"></i>
                    搜索结果 (${data.results.length} 条)
                </h5>
                <small class="text-muted">查询: "${this.currentQuery}"</small>
            </div>
        `;

        data.results.forEach((result, index) => {
            html += this.createResultCard(result, index + 1);
        });

        resultsContainer.innerHTML = html;
        
        // 添加动画效果
        resultsContainer.querySelectorAll('.result-item').forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('fade-in-up');
            }, index * 100);
        });
    }

    createResultCard(result, index) {
        const scoreColor = result.overall_score > 0.7 ? 'success' : 
                          result.overall_score > 0.4 ? 'warning' : 'secondary';

        return `
            <div class="result-item">
                <div class="result-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-light text-dark me-2">#${index}</span>
                            <span class="result-id">${result.id}</span>
                        </div>
                        <span class="result-score bg-${scoreColor}">
                            ${(result.overall_score * 100).toFixed(1)}%
                        </span>
                    </div>
                </div>
                <div class="result-body">
                    <div class="result-description">
                        ${this.highlightKeywords(result.description, result.matched_keywords)}
                    </div>
                    
                    <div class="result-meta">
                        <div class="meta-item">
                            <span class="meta-label">数据集</span>
                            <span class="meta-value">${result.dataset_name}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">类别</span>
                            <span class="meta-value">${result.category_name}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">子类别</span>
                            <span class="meta-value">${result.subcategory_name}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">语义分数</span>
                            <span class="meta-value">${(result.semantic_score * 100).toFixed(1)}%</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">关键词分数</span>
                            <span class="meta-value">${(result.keyword_score * 100).toFixed(1)}%</span>
                        </div>
                    </div>

                    <div class="matched-info">
                        <div class="mb-2">
                            <small class="text-muted">匹配列:</small>
                            <div class="matched-columns">
                                ${result.matched_columns.map(col => 
                                    `<span class="column-badge">${col}</span>`
                                ).join('')}
                            </div>
                        </div>
                        
                        ${result.matched_keywords.length > 0 ? `
                        <div>
                            <small class="text-muted">匹配关键词:</small>
                            <div class="matched-keywords">
                                ${result.matched_keywords.slice(0, 8).map(keyword => 
                                    `<span class="keyword-badge">${keyword}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    highlightKeywords(text, keywords) {
        if (!keywords || keywords.length === 0) return text;
        
        let highlightedText = text;
        keywords.forEach(keyword => {
            const regex = new RegExp(`(${keyword})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    updateThresholdStatus(stats) {
        if (!stats || !stats.current_thresholds) return;

        const thresholds = stats.current_thresholds;
        
        document.getElementById('min-threshold').textContent = thresholds.min_score.toFixed(3);
        document.getElementById('semantic-threshold').textContent = thresholds.semantic.toFixed(3);
        document.getElementById('keyword-threshold').textContent = thresholds.keyword.toFixed(3);
        document.getElementById('preference-score').textContent = stats.preference_score.toFixed(2);

        document.getElementById('threshold-status').style.display = 'block';
    }

    updateSearchStats(data) {
        document.getElementById('search-time').textContent = `${data.search_time}s`;
        document.getElementById('result-count').textContent = data.total_results;
        
        if (data.session_stats) {
            document.getElementById('total-searches').textContent = data.session_stats.total_searches;
            document.getElementById('threshold-trend').textContent = data.session_stats.threshold_trend;
        }

        document.getElementById('search-stats').style.display = 'block';
    }

    showLoading() {
        document.getElementById('loading-indicator').style.display = 'block';
        document.getElementById('search-btn').disabled = true;
        document.getElementById('search-btn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>搜索中...';
    }

    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
        document.getElementById('search-btn').disabled = false;
        document.getElementById('search-btn').innerHTML = '<i class="fas fa-search me-1"></i>搜索';
    }

    showError(message) {
        const resultsContainer = document.getElementById('search-results');
        resultsContainer.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>错误:</strong> ${message}
            </div>
        `;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new BERTSearchApp();
});
