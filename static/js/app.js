// BERT融合搜索引擎 - 前端交互逻辑

class BERTSearchApp {
    constructor() {
        this.currentQuery = '';
        this.isSearching = false;
        this.allResults = [];
        this.displayedResults = 0;
        this.resultsPerPage = 99999;  // 移除分页限制，显示所有结果
        this.isTranslated = false;  // 翻译状态
        this.isTranslating = false;  // 翻译进行中

        // 筛选相关
        this.activeFilters = {
            category: '',
            subcategory: '',
            datasetType: ''
        };
        this.filteredResults = [];

        // 组合因子相关
        this.numeratorList = [];
        this.denominatorList = [];
        this.combinationFactors = [];

        this.init();
    }

    init() {
        console.log('🚀 App v2.0 initializing...');
        this.bindEvents();
        this.checkSystemStatus();
        this.showInitializationProgress();

        // 添加调试信息
        window.debugApp = this;
        console.log('🔧 Debug: App instance available as window.debugApp');
        console.log('🔧 Debug: Combination factors array:', this.combinationFactors);
    }

    bindEvents() {
        // 搜索按钮事件
        document.getElementById('search-btn').addEventListener('click', () => {
            this.performSearch();
        });

        // 搜索输入框回车事件
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 快速查询按钮事件
        document.querySelectorAll('.quick-query').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const query = e.target.getAttribute('data-query');
                document.getElementById('search-input').value = query;
                this.performSearch();
            });
        });

        // 阈值调整按钮事件
        document.getElementById('more-results-btn').addEventListener('click', () => {
            this.adjustThreshold('more_results');
        });

        document.getElementById('more-precise-btn').addEventListener('click', () => {
            this.adjustThreshold('more_precise');
        });

        document.getElementById('reset-btn').addEventListener('click', () => {
            this.adjustThreshold('reset');
        });

        // 数据集加载按钮事件
        document.getElementById('load-dataset-btn').addEventListener('click', () => {
            this.loadDataset();
        });

        // 翻译按钮事件（已移除全局翻译按钮，改为单个翻译）
        // document.getElementById('translate-btn').addEventListener('click', () => {
        //     this.toggleTranslation();
        // });

        // 筛选相关事件
        const applyFiltersBtn = document.getElementById('apply-filters-btn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Apply filters button clicked');
                this.applyFilters();
            });
            console.log('Apply filters button event bound successfully');
        } else {
            console.error('Apply filters button not found');
        }

        // 初始状态下禁用筛选按钮
        if (applyFiltersBtn) {
            applyFiltersBtn.disabled = true;
            applyFiltersBtn.innerHTML = '<i class="fas fa-filter me-1"></i>请先搜索';
        }

        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                console.log('Clear filters button clicked');
                this.clearFilters();
            });
        } else {
            console.error('Clear filters button not found');
        }

        // 筛选下拉框回车事件（可选）
        document.getElementById('category-filter').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });

        document.getElementById('subcategory-filter').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });

        document.getElementById('dataset-type-filter').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });

        // 组合因子相关事件
        this.bindCombinationEvents();
    }

    async checkSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.success) {
                const initStatus = data.initialization_status;

                if (initStatus.status === 'completed' && data.data_loaded) {
                    this.updateStatusIndicator('ready', '系统就绪');
                    this.showWelcomeMessage();
                } else if (initStatus.status === 'initializing') {
                    this.updateStatusIndicator('loading', `初始化中... ${initStatus.progress}%`);
                    this.showInitializationProgress(initStatus);
                    // 继续检查状态
                    setTimeout(() => this.checkSystemStatus(), 2000);
                } else if (initStatus.status === 'error') {
                    this.updateStatusIndicator('error', '初始化失败');
                    this.showError(initStatus.message);
                } else {
                    this.updateStatusIndicator('loading', '准备初始化...');
                    setTimeout(() => this.checkSystemStatus(), 1000);
                }
            } else {
                this.updateStatusIndicator('error', '系统未就绪');
            }
        } catch (error) {
            console.error('Status check failed:', error);
            this.updateStatusIndicator('error', '连接失败');
        }
    }

    updateStatusIndicator(status, message) {
        const indicator = document.getElementById('status-indicator');
        const iconClass = status === 'ready' ? 'text-success' : 
                         status === 'loading' ? 'text-warning' : 'text-danger';
        
        indicator.innerHTML = `
            <i class="fas fa-circle ${iconClass} me-1"></i>
            ${message}
        `;
    }

    showInitializationProgress(initStatus = null) {
        const resultsContainer = document.getElementById('search-results');

        if (initStatus && initStatus.status === 'initializing') {
            resultsContainer.innerHTML = `
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-cog fa-spin text-primary mb-3" style="font-size: 4rem;"></i>
                        <h4 class="card-title">正在初始化BERT融合搜索引擎</h4>
                        <p class="card-text text-muted mb-4">${initStatus.message}</p>

                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 style="width: ${initStatus.progress}%"
                                 aria-valuenow="${initStatus.progress}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                ${initStatus.progress}%
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <i class="fas fa-download text-info mb-2" style="font-size: 1.5rem;"></i>
                                <h6>模型预加载</h6>
                                <small class="text-muted">预加载BERT模型到内存</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-database text-success mb-2" style="font-size: 1.5rem;"></i>
                                <h6>向量缓存</h6>
                                <small class="text-muted">构建高速向量缓存系统</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-search text-warning mb-2" style="font-size: 1.5rem;"></i>
                                <h6>索引构建</h6>
                                <small class="text-muted">构建优化的搜索索引</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            resultsContainer.innerHTML = `
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-hourglass-half text-primary mb-3" style="font-size: 4rem;"></i>
                        <h4 class="card-title">系统准备中...</h4>
                        <p class="card-text text-muted">
                            正在启动BERT融合搜索引擎，请稍候...
                        </p>
                    </div>
                </div>
            `;
        }
    }

    showWelcomeMessage() {
        const resultsContainer = document.getElementById('search-results');
        resultsContainer.innerHTML = `
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-rocket text-primary mb-3" style="font-size: 4rem;"></i>
                    <h4 class="card-title">🎉 BERT融合搜索引擎已就绪</h4>
                    <p class="card-text text-muted">
                        ⚡ 优化版本：模型预加载 + 向量缓存 + 索引持久化
                    </p>
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <i class="fas fa-brain text-info mb-2" style="font-size: 2rem;"></i>
                            <h6>BERT多模型融合</h6>
                            <small class="text-muted">预加载的深度学习模型</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-tachometer-alt text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>高速缓存</h6>
                            <small class="text-muted">向量缓存 + 索引持久化</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-sliders-h text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>动态阈值</h6>
                            <small class="text-muted">智能阈值调整</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-language text-danger mb-2" style="font-size: 2rem;"></i>
                            <h6>中英文支持</h6>
                            <small class="text-muted">跨语言语义理解</small>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-outline-primary me-2" onclick="this.showPerformanceStats()">
                            <i class="fas fa-chart-line me-1"></i>
                            查看性能统计
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async showPerformanceStats() {
        try {
            const response = await fetch('/api/performance');
            const data = await response.json();

            if (data.success) {
                const stats = data.performance_stats;
                const resultsContainer = document.getElementById('search-results');

                resultsContainer.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                系统性能统计
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>搜索性能</h6>
                                    <ul class="list-unstyled">
                                        <li>总搜索次数: ${stats.search_stats.total_searches}</li>
                                        <li>平均搜索时间: ${stats.search_stats.avg_search_time.toFixed(3)}s</li>
                                        <li>索引构建时间: ${stats.search_stats.index_build_time.toFixed(2)}s</li>
                                        <li>已索引文档: ${stats.indexed_documents}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>缓存性能</h6>
                                    <ul class="list-unstyled">
                                        <li>缓存命中率: ${(stats.model_stats.cache_hit_rate * 100).toFixed(1)}%</li>
                                        <li>总请求数: ${stats.model_stats.total_requests}</li>
                                        <li>平均编码时间: ${stats.model_stats.avg_encoding_time.toFixed(3)}s</li>
                                        <li>已加载模型: ${stats.model_stats.loaded_models.join(', ')}</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>缓存大小</h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <small class="text-muted">向量缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.vector_cache.toFixed(1)} MB</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">索引缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.index_cache.toFixed(1)} MB</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">模型缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.model_cache.toFixed(1)} MB</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">总缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.total_cache.toFixed(1)} MB</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary" onclick="location.reload()">
                                    <i class="fas fa-home me-1"></i>
                                    返回首页
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Failed to load performance stats:', error);
        }
    }

    async performSearch() {
        if (this.isSearching) return;

        const query = document.getElementById('search-input').value.trim();
        if (!query) {
            this.showError('请输入搜索查询');
            return;
        }

        this.currentQuery = query;
        this.isSearching = true;
        this.showLoading();
        this.updateStatusIndicator('loading', '搜索中...');

        try {
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
                this.updateThresholdStatus(data.session_stats);
                this.updateSearchStats(data);
            } else {
                this.showError(data.error || '搜索失败');
            }
        } catch (error) {
            console.error('Search failed:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateStatusIndicator('ready', '系统就绪');
        }
    }

    async adjustThreshold(action) {
        if (this.isSearching) return;

        this.isSearching = true;
        this.showLoading();
        this.updateStatusIndicator('loading', '调整阈值中...');

        try {
            const response = await fetch('/api/adjust_threshold', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: action })
            });

            const data = await response.json();

            if (data.success) {
                this.updateThresholdStatus(data.session_stats);
                
                if (data.results && data.results.length > 0) {
                    // 如果有重新搜索的结果，显示它们
                    this.displayResults({
                        results: data.results,
                        search_time: 0,
                        total_results: data.results.length
                    });
                }

                // 显示调整提示
                this.showAdjustmentMessage(action);
            } else {
                this.showError(data.error || '阈值调整失败');
            }
        } catch (error) {
            console.error('Threshold adjustment failed:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateStatusIndicator('ready', '系统就绪');
        }
    }

    showAdjustmentMessage(action) {
        const messages = {
            'more_results': '已降低阈值，获得更多结果',
            'more_precise': '已提高阈值，获得更精准结果',
            'reset': '已重置阈值到默认值'
        };

        const message = messages[action] || '阈值已调整';
        
        // 创建临时提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alert, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }

    displayResults(data) {
        const overviewContainer = document.getElementById('results-overview');
        const detailsContainer = document.getElementById('result-details');

        if (!data.results || data.results.length === 0) {
            overviewContainer.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5>未找到匹配结果</h5>
                        <p class="text-muted">尝试使用不同的关键词，或点击"更多结果"降低搜索阈值</p>
                    </div>
                </div>
            `;

            detailsContainer.innerHTML = `
                <div class="card">
                    <div class="card-body text-center text-muted">
                        <i class="fas fa-mouse-pointer mb-3" style="font-size: 3rem;"></i>
                        <h5>点击左侧因子查看详细信息</h5>
                        <p>选择一个因子以查看完整的描述、分类信息和匹配详情</p>
                    </div>
                </div>
            `;
            return;
        }

        // 保存所有结果
        this.allResults = data.results;
        this.displayedResults = 0;

        // 显示结果总览
        let overviewHtml = `
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h5 class="mb-0">
                                <i class="fas fa-list-ul me-2 text-primary"></i>
                                搜索结果 (${data.results.length} 条)
                            </h5>
                            <small class="text-muted">查询: "${this.currentQuery}" | 勾选因子进行批量操作</small>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <div class="d-flex align-items-center mb-2">
                                <small class="text-muted me-2">得分筛选:</small>
                                <select class="form-select form-select-sm me-2" id="score-threshold-select" style="width: 110px;">
                                    <option value="0">全部</option>
                                    <option value="0.1">≥10%</option>
                                    <option value="0.15">≥15%</option>
                                    <option value="0.2">≥20%</option>
                                    <option value="0.25">≥25%</option>
                                    <option value="0.3">≥30%</option>
                                    <option value="0.35">≥35%</option>
                                    <option value="0.4">≥40%</option>
                                    <option value="0.45">≥45%</option>
                                    <option value="0.5">≥50%</option>
                                    <option value="0.55">≥55%</option>
                                    <option value="0.6">≥60%</option>
                                    <option value="0.65">≥65%</option>
                                    <option value="0.7">≥70%</option>
                                    <option value="0.75">≥75%</option>
                                    <option value="0.8">≥80%</option>
                                    <option value="0.85">≥85%</option>
                                    <option value="0.9">≥90%</option>
                                    <option value="0.95">≥95%</option>
                                </select>
                                <button class="btn btn-outline-success btn-sm me-1" id="select-by-score-btn">
                                    <i class="fas fa-star me-1"></i>按分选择
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" id="select-none-btn">
                                    <i class="fas fa-square me-1"></i>清除
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="btn-group me-2" role="group">
                                    <button class="btn btn-outline-secondary btn-sm" id="select-all-btn">
                                        <i class="fas fa-check-square me-1"></i>全选
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" id="select-top-btn">
                                        <i class="fas fa-trophy me-1"></i>前10名
                                    </button>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-primary btn-sm" id="batch-add-numerator-btn" disabled>
                                        <i class="fas fa-arrow-up me-1"></i>批量分子
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="batch-add-denominator-btn" disabled>
                                        <i class="fas fa-arrow-down me-1"></i>批量分母
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="results-list">
                    </div>
                    <!-- 移除加载更多按钮，显示所有结果 -->
                </div>
            </div>
        `;

        overviewContainer.innerHTML = overviewHtml;

        // 移除分页限制
        this.resultsPerPage = 99999;  // 显示所有结果

        // 加载第一页结果
        this.loadMoreResults();

        // 绑定加载更多按钮事件
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreResults();
            });
        }

        // 默认显示第一个结果的详情
        if (data.results.length > 0) {
            this.showResultDetails(data.results[0], 1);
        }

        // 初始化筛选结果（显示所有结果）
        this.filteredResults = this.allResults;

        // 启用筛选按钮
        const applyFiltersBtn = document.getElementById('apply-filters-btn');
        if (applyFiltersBtn) {
            applyFiltersBtn.disabled = false;
            applyFiltersBtn.innerHTML = '<i class="fas fa-filter me-1"></i>应用筛选';
        }

        // 绑定批量操作事件
        this.bindBatchOperationEvents();

        // 更新得分筛选选项
        this.updateScoreThresholdOptions();
    }

    renderResults() {
        // 重新渲染所有结果（用于翻译切换）
        const resultsList = document.getElementById('results-list');
        if (!resultsList) return;

        resultsList.innerHTML = '';
        this.displayedResults = 0;

        // 移除分页限制
        this.resultsPerPage = 99999;
        this.loadMoreResults();

        // 重新显示当前选中的结果详情
        const activeItem = document.querySelector('.result-overview-item.active');
        if (activeItem) {
            const index = parseInt(activeItem.dataset.index);
            this.showResultDetails(this.allResults[index], index + 1);
        }
    }

    loadMoreResults() {
        const resultsList = document.getElementById('results-list');
        const loadMoreBtn = document.getElementById('load-more-btn');

        const startIndex = this.displayedResults;
        const endIndex = Math.min(startIndex + this.resultsPerPage, this.allResults.length);

        for (let i = startIndex; i < endIndex; i++) {
            const result = this.allResults[i];
            const index = i;
            const scoreColor = result.overall_score > 0.7 ? 'success' :
                              result.overall_score > 0.4 ? 'warning' : 'secondary';

            const itemHtml = `
                <div class="list-group-item list-group-item-action result-overview-item compact-item"
                     data-index="${index}"
                     style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center me-2">
                            <input type="checkbox" class="form-check-input me-2 factor-checkbox"
                                   data-factor-id="${result.id}" data-index="${index}"
                                   onclick="event.stopPropagation();">
                            <div class="flex-grow-1">
                                <div class="fw-bold text-primary small">${result.id}</div>
                                <div class="text-muted" style="font-size: 0.75rem; line-height: 1.2;">
                                    ${result.dataset_name_translated || result.dataset_name}
                                </div>
                            </div>
                        </div>
                        <div class="text-end d-flex align-items-center">
                            <button class="btn btn-outline-primary btn-sm me-2 translate-single-btn"
                                    data-index="${index}"
                                    style="font-size: 0.7rem; padding: 0.2rem 0.4rem;">
                                <i class="fas fa-language"></i>
                            </button>
                            <div>
                                <span class="badge bg-${scoreColor}" style="font-size: 0.7rem;">
                                    ${(result.overall_score * 100).toFixed(0)}%
                                </span>
                                <div class="text-muted" style="font-size: 0.65rem;">#${index + 1}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            resultsList.insertAdjacentHTML('beforeend', itemHtml);
        }

        this.displayedResults = endIndex;

        // 绑定新添加项目的点击事件
        const newItems = resultsList.querySelectorAll('.result-overview-item:not(.bound)');
        newItems.forEach(item => {
            item.classList.add('bound');
            item.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.showResultDetails(this.allResults[index], index + 1);

                // 更新选中状态
                document.querySelectorAll('.result-overview-item').forEach(i => i.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // 绑定翻译按钮事件
        const newTranslateButtons = resultsList.querySelectorAll('.translate-single-btn:not(.bound)');
        newTranslateButtons.forEach(button => {
            button.classList.add('bound');
            button.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止触发父元素的点击事件
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.translateSingleResult(index);
            });
        });

        // 更新加载更多按钮
        if (loadMoreBtn) {
            if (this.displayedResults >= this.allResults.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.innerHTML = `
                    <i class="fas fa-chevron-down me-1"></i>
                    加载更多结果 (${this.displayedResults}/${this.allResults.length})
                `;
            }
        }

        // 添加动画效果
        newItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('fade-in-up');
            }, index * 50);
        });

        // 如果是第一次加载，显示第一个结果的详情
        if (startIndex === 0 && this.allResults.length > 0) {
            this.showResultDetails(this.allResults[0], 1);
            document.querySelector('.result-overview-item').classList.add('active');
        }
    }

    showResultDetails(result, rank) {
        const detailsContainer = document.getElementById('result-details');

        const scoreColor = result.overall_score > 0.7 ? 'success' :
                          result.overall_score > 0.4 ? 'warning' : 'secondary';

        const detailsHtml = `
            <div class="card">
                <div class="card-header bg-${scoreColor} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">${result.id_translated || result.id}</h5>
                            <small class="opacity-75">${result.id}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-light text-dark fs-6">排名 #${rank}</span>
                            <div class="mt-1">
                                <small>综合得分: ${(result.overall_score * 100).toFixed(1)}%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 因子描述 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-info-circle me-2"></i>
                            因子描述
                        </h6>
                        <div class="bg-light p-3 rounded">
                            ${result.showTranslation && result.description_translated ?
                                `<p class="mb-2"><strong>中文:</strong> ${result.description_translated}</p>
                                 <p class="mb-0 small text-muted"><strong>原文:</strong> ${result.description}</p>` :
                                `<p class="mb-0">${result.description}</p>`
                            }
                        </div>
                    </div>

                    <!-- 分类信息 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-tags me-2"></i>
                            分类信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>数据集:</strong><br>
                                    <span class="text-primary">${result.dataset_name_translated || result.dataset_name}</span>
                                    ${result.dataset_name_translated ? `<br><small class="text-muted">${result.dataset_name}</small>` : ''}
                                </div>
                                <div class="mb-2">
                                    <strong>主类别:</strong><br>
                                    <span class="text-success">${result.category_name_translated || result.category_name}</span>
                                    ${result.category_name_translated ? `<br><small class="text-muted">${result.category_name}</small>` : ''}
                                </div>
                                <div class="mb-2">
                                    <strong>地区:</strong><br>
                                    <span class="text-warning">${result.region || 'N/A'}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>子类别:</strong><br>
                                    <span class="text-info">${result.subcategory_name_translated || result.subcategory_name}</span>
                                    ${result.subcategory_name_translated ? `<br><small class="text-muted">${result.subcategory_name}</small>` : ''}
                                </div>
                                <div class="mb-2">
                                    <strong>数据延迟:</strong><br>
                                    <span class="text-secondary">${result.delay || 'N/A'}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>数据范围:</strong><br>
                                    <span class="text-muted">${result.universe || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 评分信息 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-chart-bar me-2"></i>
                            评分详情
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <strong>综合得分</strong>
                                    </div>
                                    <span class="badge bg-${scoreColor} fs-6">${(result.overall_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <strong>语义得分</strong>
                                    </div>
                                    <span class="badge bg-info fs-6">${(result.semantic_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <strong>关键词得分</strong>
                                    </div>
                                    <span class="badge bg-success fs-6">${(result.keyword_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 匹配信息 -->
                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-search me-2"></i>
                            匹配信息
                        </h6>

                        ${result.matched_columns.length > 0 ? `
                        <div class="mb-3">
                            <strong>匹配字段:</strong>
                            <div class="mt-2">
                                ${result.matched_columns.map(col =>
                                    `<span class="badge bg-primary me-1 mb-1">${col}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${result.matched_keywords.length > 0 ? `
                        <div>
                            <strong>匹配关键词:</strong>
                            <div class="mt-2">
                                ${result.matched_keywords.map(keyword =>
                                    `<span class="badge bg-success me-1 mb-1">${keyword}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>

                    <!-- 组合因子操作 -->
                    <div class="mb-3">
                        <h6 class="text-success">
                            <i class="fas fa-calculator me-2"></i>
                            添加到组合因子
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-primary w-100 add-numerator-btn" data-factor-id="${result.id}">
                                    <i class="fas fa-arrow-up me-1"></i>添加为分子
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-warning w-100 add-denominator-btn" data-factor-id="${result.id}">
                                    <i class="fas fa-arrow-down me-1"></i>添加为分母
                                </button>
                            </div>
                        </div>
                        <small class="text-muted mt-2 d-block">添加到组合因子列表后，可以进行批量组合生成</small>
                    </div>
                </div>
            </div>
        `;

        detailsContainer.innerHTML = detailsHtml;

        // 绑定添加到组合因子的按钮事件
        const addNumeratorBtn = detailsContainer.querySelector('.add-numerator-btn');
        const addDenominatorBtn = detailsContainer.querySelector('.add-denominator-btn');

        if (addNumeratorBtn) {
            addNumeratorBtn.addEventListener('click', (e) => {
                const factorId = e.currentTarget.getAttribute('data-factor-id');
                this.addToNumeratorList(factorId);
            });
        }

        if (addDenominatorBtn) {
            addDenominatorBtn.addEventListener('click', (e) => {
                const factorId = e.currentTarget.getAttribute('data-factor-id');
                this.addToDenominatorList(factorId);
            });
        }
        detailsContainer.classList.add('fade-in-up');
    }



    highlightKeywords(text, keywords) {
        if (!keywords || keywords.length === 0) return text;
        
        let highlightedText = text;
        keywords.forEach(keyword => {
            const regex = new RegExp(`(${keyword})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    updateThresholdStatus(stats) {
        if (!stats || !stats.current_thresholds) return;

        const thresholds = stats.current_thresholds;
        
        document.getElementById('min-threshold').textContent = thresholds.min_score.toFixed(3);
        document.getElementById('semantic-threshold').textContent = thresholds.semantic.toFixed(3);
        document.getElementById('keyword-threshold').textContent = thresholds.keyword.toFixed(3);
        document.getElementById('preference-score').textContent = stats.preference_score.toFixed(2);

        document.getElementById('threshold-status').style.display = 'block';
    }

    updateSearchStats(data) {
        document.getElementById('search-time').textContent = `${data.search_time}s`;
        document.getElementById('result-count').textContent = data.total_results;
        
        if (data.session_stats) {
            document.getElementById('total-searches').textContent = data.session_stats.total_searches;
            document.getElementById('threshold-trend').textContent = data.session_stats.threshold_trend;
        }

        document.getElementById('search-stats').style.display = 'block';
    }

    showLoading() {
        document.getElementById('loading-indicator').style.display = 'block';
        document.getElementById('search-btn').disabled = true;
        document.getElementById('search-btn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>搜索中...';
    }

    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
        document.getElementById('search-btn').disabled = false;
        document.getElementById('search-btn').innerHTML = '<i class="fas fa-search me-1"></i>搜索';
    }

    showError(message) {
        const overviewContainer = document.getElementById('results-overview');
        overviewContainer.innerHTML = `
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle text-danger mb-3" style="font-size: 3rem;"></i>
                    <h5>错误</h5>
                    <p class="text-muted">${message}</p>
                </div>
            </div>
        `;
    }

    async loadDataset() {
        const selectElement = document.getElementById('dataset-select');
        const filename = selectElement.value;

        if (!filename) {
            alert('请先选择一个数据集');
            return;
        }

        const loadBtn = document.getElementById('load-dataset-btn');
        const originalText = loadBtn.innerHTML;

        try {
            // 显示加载状态
            loadBtn.disabled = true;
            loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';

            this.updateStatusIndicator('loading', '正在加载数据集...');

            const response = await fetch('/api/load_dataset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filename: filename })
            });

            const data = await response.json();

            if (data.success) {
                // 更新数据集信息显示
                const selectedOption = selectElement.options[selectElement.selectedIndex];
                document.getElementById('dataset-info').textContent =
                    `当前: ${selectedOption.text} (${data.sample_size}/${data.total_records} 条记录)`;

                this.updateStatusIndicator('ready', '数据集加载完成');

                // 清空搜索结果
                document.getElementById('results-overview').innerHTML = '';
                document.getElementById('result-details').innerHTML = `
                    <div class="card">
                        <div class="card-body text-center text-muted">
                            <i class="fas fa-database mb-3 text-success" style="font-size: 3rem;"></i>
                            <h5>数据集已更新</h5>
                            <p>新数据集已加载完成，现在可以开始搜索了</p>
                        </div>
                    </div>
                `;

                // 显示成功提示
                this.showSuccessMessage(`成功加载数据集: ${selectedOption.text}`);

            } else {
                this.updateStatusIndicator('error', '数据集加载失败');
                this.showError(data.error || '数据集加载失败');
            }

        } catch (error) {
            console.error('Dataset loading failed:', error);
            this.updateStatusIndicator('error', '连接失败');
            this.showError('网络错误，请重试');
        } finally {
            loadBtn.disabled = false;
            loadBtn.innerHTML = originalText;
        }
    }

    showSuccessMessage(message) {
        // 创建临时成功提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alert, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }

    async translateSingleResult(index) {
        const result = this.allResults[index];
        const translateBtn = document.querySelector(`[data-index="${index}"].translate-single-btn`);

        if (!result || !translateBtn) return;

        // 如果已经翻译过，切换显示状态
        if (result.description_translated) {
            result.showTranslation = !result.showTranslation;
            this.updateSingleResultDisplay(index);
            this.updateTranslateButtonState(translateBtn, result);
            return;
        }

        // 执行翻译
        const originalHtml = translateBtn.innerHTML;

        try {
            translateBtn.disabled = true;
            translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    texts: [result.description]
                })
            });

            const data = await response.json();

            if (data.success && data.translations.length > 0) {
                // 保存翻译结果
                result.description_translated = data.translations[0];
                result.showTranslation = true;

                // 更新显示
                this.updateSingleResultDisplay(index);
                this.updateTranslateButtonState(translateBtn, result);

                // 如果当前选中的是这个结果，更新详情显示
                const activeItem = document.querySelector('.result-overview-item.active');
                if (activeItem && parseInt(activeItem.dataset.index) === index) {
                    this.showResultDetails(result, index + 1);
                }

                this.showSuccessMessage(`翻译完成！用时 ${data.translation_time}s`);
            } else {
                this.showError(data.error || '翻译失败');
            }

        } catch (error) {
            console.error('Translation failed:', error);
            this.showError('翻译失败，请重试');
        } finally {
            translateBtn.disabled = false;
            if (!result.description_translated) {
                translateBtn.innerHTML = originalHtml;
            }
        }
    }

    updateTranslateButtonState(button, result) {
        if (result.description_translated) {
            if (result.showTranslation) {
                button.innerHTML = '<i class="fas fa-eye-slash"></i>';
                button.className = 'btn btn-outline-secondary btn-sm me-2 translate-single-btn';
                button.title = '显示原文';
            } else {
                button.innerHTML = '<i class="fas fa-language"></i>';
                button.className = 'btn btn-outline-success btn-sm me-2 translate-single-btn';
                button.title = '显示翻译';
            }
        } else {
            button.innerHTML = '<i class="fas fa-language"></i>';
            button.className = 'btn btn-outline-primary btn-sm me-2 translate-single-btn';
            button.title = '翻译';
        }
    }

    updateSingleResultDisplay(index) {
        // 这个方法可以用来更新单个结果的显示状态
        // 目前主要通过详情面板显示翻译，所以这里暂时不需要特殊处理
    }

    // 筛选相关方法
    applyFilters() {
        console.log('applyFilters called');

        if (!this.allResults || this.allResults.length === 0) {
            console.log('No results to filter');
            this.showError('请先进行搜索，然后再应用筛选');
            return;
        }

        // 检查results-overview是否存在
        const resultsContainer = document.getElementById('results-overview');
        if (!resultsContainer) {
            console.log('Results overview not found - no search performed yet');
            this.showError('请先进行搜索，然后再应用筛选');
            return;
        }

        // 获取当前筛选条件
        this.activeFilters.category = document.getElementById('category-filter').value;
        this.activeFilters.subcategory = document.getElementById('subcategory-filter').value;
        this.activeFilters.datasetType = document.getElementById('dataset-type-filter').value;

        console.log('Filter conditions:', this.activeFilters);

        // 应用筛选
        this.filteredResults = this.allResults.filter(result => {
            // 主类别筛选
            if (this.activeFilters.category && result.category_name !== this.activeFilters.category) {
                console.log(`Category filter: ${result.category_name} !== ${this.activeFilters.category}`);
                return false;
            }

            // 子类别筛选
            if (this.activeFilters.subcategory && result.subcategory_name !== this.activeFilters.subcategory) {
                console.log(`Subcategory filter: ${result.subcategory_name} !== ${this.activeFilters.subcategory}`);
                return false;
            }

            // 数据集类型筛选
            if (this.activeFilters.datasetType && result.dataset_name !== this.activeFilters.datasetType) {
                console.log(`Dataset filter: ${result.dataset_name} !== ${this.activeFilters.datasetType}`);
                return false;
            }

            return true;
        });

        console.log(`Filtered ${this.filteredResults.length} results from ${this.allResults.length} total`);

        // 重新渲染结果
        this.renderFilteredResults();
    }

    clearFilters() {
        // 清除筛选条件
        document.getElementById('category-filter').value = '';
        document.getElementById('subcategory-filter').value = '';
        document.getElementById('dataset-type-filter').value = '';

        this.activeFilters = {
            category: '',
            subcategory: '',
            datasetType: ''
        };

        // 显示所有结果
        this.filteredResults = this.allResults;
        this.renderFilteredResults();
    }

    renderFilteredResults() {
        // 确定要显示的结果
        const currentResults = this.filteredResults.length > 0 ? this.filteredResults : this.allResults;
        console.log('Rendering filtered results:', currentResults.length);

        // 更新结果总览标题
        const resultsContainer = document.getElementById('results-overview');
        if (!resultsContainer) {
            console.error('Results overview not found');
            return;
        }

        const hasFilters = this.activeFilters.category || this.activeFilters.subcategory || this.activeFilters.datasetType;
        const filterText = hasFilters ? ` (已筛选 ${currentResults.length}/${this.allResults.length})` : ` (${currentResults.length} 条)`;

        let overviewHtml = `
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h5 class="mb-0">
                                <i class="fas fa-list-ul me-2 text-primary"></i>
                                搜索结果${filterText}
                            </h5>
                            <small class="text-muted">查询: "${this.currentQuery}" | 勾选因子进行批量操作</small>
                            ${hasFilters ? '<div class="mt-1"><span class="badge bg-info"><i class="fas fa-filter me-1"></i>已应用筛选</span></div>' : ''}
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <div class="d-flex align-items-center mb-2">
                                <small class="text-muted me-2">得分筛选:</small>
                                <select class="form-select form-select-sm me-2" id="score-threshold-select" style="width: 110px;">
                                    <option value="0">全部</option>
                                    <option value="0.1">≥10%</option>
                                    <option value="0.15">≥15%</option>
                                    <option value="0.2">≥20%</option>
                                    <option value="0.25">≥25%</option>
                                    <option value="0.3">≥30%</option>
                                    <option value="0.35">≥35%</option>
                                    <option value="0.4">≥40%</option>
                                    <option value="0.45">≥45%</option>
                                    <option value="0.5">≥50%</option>
                                    <option value="0.55">≥55%</option>
                                    <option value="0.6">≥60%</option>
                                    <option value="0.65">≥65%</option>
                                    <option value="0.7">≥70%</option>
                                    <option value="0.75">≥75%</option>
                                    <option value="0.8">≥80%</option>
                                    <option value="0.85">≥85%</option>
                                    <option value="0.9">≥90%</option>
                                    <option value="0.95">≥95%</option>
                                </select>
                                <button class="btn btn-outline-success btn-sm me-1" id="select-by-score-btn">
                                    <i class="fas fa-star me-1"></i>按分选择
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" id="select-none-btn">
                                    <i class="fas fa-square me-1"></i>清除
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="btn-group me-2" role="group">
                                    <button class="btn btn-outline-secondary btn-sm" id="select-all-btn">
                                        <i class="fas fa-check-square me-1"></i>全选
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" id="select-top-btn">
                                        <i class="fas fa-trophy me-1"></i>前10名
                                    </button>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-primary btn-sm" id="batch-add-numerator-btn" disabled>
                                        <i class="fas fa-arrow-up me-1"></i>批量分子
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="batch-add-denominator-btn" disabled>
                                        <i class="fas fa-arrow-down me-1"></i>批量分母
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="results-list">
                        <!-- 结果将在这里动态加载 -->
                    </div>
                    <!-- 移除加载更多按钮，显示所有结果 -->
                </div>
            </div>
        `;

        resultsContainer.innerHTML = overviewHtml;

        // 直接渲染筛选后的结果
        this.renderResultsList(currentResults);

        // 绑定加载更多按钮事件
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreFilteredResults(currentResults);
            });
        }

        // 如果有结果，默认显示第一个结果的详情
        if (currentResults.length > 0) {
            this.showResultDetails(currentResults[0], 1);
        }

        // 绑定批量操作事件
        this.bindBatchOperationEvents();

        // 更新得分筛选选项
        this.updateScoreThresholdOptions();
    }

    renderResultsList(results) {
        const resultsList = document.getElementById('results-list');
        if (!resultsList) return;

        // 清空现有结果
        resultsList.innerHTML = '';
        this.displayedResults = 0;

        // 显示第一页结果
        const endIndex = Math.min(this.resultsPerPage, results.length);

        for (let i = 0; i < endIndex; i++) {
            const result = results[i];
            this.addResultItem(result, i, resultsList);
        }

        this.displayedResults = endIndex;
        console.log(`Displayed ${this.displayedResults} results`);
    }

    loadMoreFilteredResults(results) {
        const resultsList = document.getElementById('results-list');
        if (!resultsList) return;

        const startIndex = this.displayedResults;
        const endIndex = Math.min(startIndex + this.resultsPerPage, results.length);

        for (let i = startIndex; i < endIndex; i++) {
            const result = results[i];
            this.addResultItem(result, i, resultsList);
        }

        this.displayedResults = endIndex;

        // 更新加载更多按钮
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            if (this.displayedResults >= results.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.innerHTML = `
                    <i class="fas fa-chevron-down me-1"></i>
                    加载更多结果 (${this.displayedResults}/${results.length})
                `;
            }
        }
    }

    addResultItem(result, index, container) {
        // 计算分数颜色
        const score = result.overall_score;
        let scoreColor = 'secondary';
        if (score >= 0.8) scoreColor = 'success';
        else if (score >= 0.6) scoreColor = 'primary';
        else if (score >= 0.4) scoreColor = 'warning';

        const itemHtml = `
            <div class="list-group-item list-group-item-action result-overview-item compact-item"
                 data-index="${index}"
                 style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1 me-2">
                        <div class="fw-bold text-primary small">${result.id}</div>
                        <div class="text-muted" style="font-size: 0.75rem; line-height: 1.2;">
                            ${result.dataset_name_translated || result.dataset_name}
                        </div>
                    </div>
                    <div class="text-end d-flex align-items-center">
                        <button class="btn btn-outline-primary btn-sm me-2 translate-single-btn"
                                data-index="${index}"
                                style="font-size: 0.7rem; padding: 0.2rem 0.4rem;">
                            <i class="fas fa-language"></i>
                        </button>
                        <div>
                            <span class="badge bg-${scoreColor}" style="font-size: 0.7rem;">
                                ${(result.overall_score * 100).toFixed(0)}%
                            </span>
                            <div class="text-muted" style="font-size: 0.65rem;">#${index + 1}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', itemHtml);

        // 绑定点击事件
        const newItem = container.lastElementChild;
        newItem.addEventListener('click', (e) => {
            const clickedIndex = parseInt(e.currentTarget.getAttribute('data-index'));
            this.showResultDetails(result, clickedIndex + 1);

            // 更新选中状态
            document.querySelectorAll('.result-overview-item').forEach(i => i.classList.remove('active'));
            e.currentTarget.classList.add('active');
        });

        // 绑定翻译按钮事件
        const translateBtn = newItem.querySelector('.translate-single-btn');
        if (translateBtn) {
            translateBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const clickedIndex = parseInt(e.currentTarget.getAttribute('data-index'));
                this.translateSingleResult(clickedIndex);
            });
        }
    }

    getFilterSummary() {
        const filters = [];
        if (this.activeFilters.category) {
            filters.push(`主类别: ${this.activeFilters.category}`);
        }
        if (this.activeFilters.subcategory) {
            filters.push(`子类别: ${this.activeFilters.subcategory}`);
        }
        if (this.activeFilters.datasetType) {
            filters.push(`数据集: ${this.activeFilters.datasetType}`);
        }
        return filters.join(', ');
    }

    // ==================== 组合因子功能 ====================

    bindCombinationEvents() {
        // 清空列表按钮
        document.getElementById('clear-numerator-btn').addEventListener('click', () => {
            this.clearNumeratorList();
        });

        document.getElementById('clear-denominator-btn').addEventListener('click', () => {
            this.clearDenominatorList();
        });

        // 批量操作按钮
        document.getElementById('batch-generate-btn').addEventListener('click', () => {
            this.batchGenerateCombinations();
        });

        document.getElementById('export-txt-btn').addEventListener('click', () => {
            this.exportCombinationToTxt();
        });

        document.getElementById('preview-combinations-btn').addEventListener('click', () => {
            this.previewCombinations();
        });

        document.getElementById('clear-all-btn').addEventListener('click', () => {
            this.clearAllCombinations();
        });
    }

    addToNumeratorList(factorId) {
        // 从当前搜索结果中找到对应的因子
        const factor = this.allResults.find(result => result.id === factorId);
        if (!factor) {
            this.showError('未找到对应的因子');
            return;
        }

        // 检查是否已经存在
        if (this.numeratorList.find(f => f.id === factorId)) {
            this.showError('该因子已在分子列表中');
            return;
        }

        this.numeratorList.push(factor);
        this.updateNumeratorList();
        this.updateCombinationButtons();
        this.showSuccessMessage(`已添加 ${factorId} 到分子列表`);
    }

    addToDenominatorList(factorId) {
        // 从当前搜索结果中找到对应的因子
        const factor = this.allResults.find(result => result.id === factorId);
        if (!factor) {
            this.showError('未找到对应的因子');
            return;
        }

        // 检查是否已经存在
        if (this.denominatorList.find(f => f.id === factorId)) {
            this.showError('该因子已在分母列表中');
            return;
        }

        this.denominatorList.push(factor);
        this.updateDenominatorList();
        this.updateCombinationButtons();
        this.showSuccessMessage(`已添加 ${factorId} 到分母列表`);
    }

    updateNumeratorList() {
        const container = document.getElementById('numerator-list');
        const countBadge = document.getElementById('numerator-count');

        countBadge.textContent = this.numeratorList.length;

        if (this.numeratorList.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <p class="mb-0">从搜索结果中添加分子因子</p>
                    <small>点击搜索结果详情中的"添加为分子"按钮</small>
                </div>
            `;
            return;
        }

        let html = '';
        this.numeratorList.forEach((factor, index) => {
            html += `
                <div class="factor-list-item p-2 border-bottom d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="fw-bold text-primary small">${factor.id}</div>
                        <div class="text-muted small" style="font-size: 0.75rem;">
                            ${factor.description.substring(0, 60)}...
                        </div>
                    </div>
                    <button class="btn btn-outline-danger btn-sm remove-numerator-btn" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });

        container.innerHTML = html;

        // 绑定删除按钮事件
        const removeButtons = container.querySelectorAll('.remove-numerator-btn');
        removeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.removeFromNumeratorList(index);
            });
        });
    }

    updateDenominatorList() {
        const container = document.getElementById('denominator-list');
        const countBadge = document.getElementById('denominator-count');

        countBadge.textContent = this.denominatorList.length;

        if (this.denominatorList.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <p class="mb-0">从搜索结果中添加分母因子</p>
                    <small>点击搜索结果详情中的"添加为分母"按钮</small>
                </div>
            `;
            return;
        }

        let html = '';
        this.denominatorList.forEach((factor, index) => {
            html += `
                <div class="factor-list-item p-2 border-bottom d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="fw-bold text-warning small">${factor.id}</div>
                        <div class="text-muted small" style="font-size: 0.75rem;">
                            ${factor.description.substring(0, 60)}...
                        </div>
                    </div>
                    <button class="btn btn-outline-danger btn-sm remove-denominator-btn" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });

        container.innerHTML = html;

        // 绑定删除按钮事件
        const removeButtons = container.querySelectorAll('.remove-denominator-btn');
        removeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.removeFromDenominatorList(index);
            });
        });
    }

    removeFromNumeratorList(index) {
        if (index >= 0 && index < this.numeratorList.length) {
            const removed = this.numeratorList.splice(index, 1)[0];
            this.updateNumeratorList();
            this.updateCombinationButtons();
            this.showSuccessMessage(`已从分子列表移除 ${removed.id}`);
        }
    }

    removeFromDenominatorList(index) {
        if (index >= 0 && index < this.denominatorList.length) {
            const removed = this.denominatorList.splice(index, 1)[0];
            this.updateDenominatorList();
            this.updateCombinationButtons();
            this.showSuccessMessage(`已从分母列表移除 ${removed.id}`);
        }
    }

    clearNumeratorList() {
        this.numeratorList = [];
        this.updateNumeratorList();
        this.updateCombinationButtons();
        this.showSuccessMessage('已清空分子列表');
    }

    clearDenominatorList() {
        this.denominatorList = [];
        this.updateDenominatorList();
        this.updateCombinationButtons();
        this.showSuccessMessage('已清空分母列表');
    }

    updateCombinationButtons() {
        const hasNumerators = this.numeratorList.length > 0;
        const hasDenominators = this.denominatorList.length > 0;
        const canGenerate = hasNumerators && hasDenominators;

        document.getElementById('batch-generate-btn').disabled = !canGenerate;
        document.getElementById('preview-combinations-btn').disabled = !canGenerate;
        document.getElementById('export-txt-btn').disabled = this.combinationFactors.length === 0;

        // 更新预览
        this.updateCombinationPreview();
    }

    updateCombinationPreview() {
        const previewContainer = document.getElementById('combination-preview');
        const countBadge = document.getElementById('combination-count');

        const numCount = this.numeratorList.length;
        const denCount = this.denominatorList.length;
        const totalCombinations = numCount * denCount;

        countBadge.textContent = this.combinationFactors.length;

        if (numCount > 0 && denCount > 0) {
            previewContainer.innerHTML = `
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h3 class="text-primary">${numCount}</h3>
                                <p class="mb-0">分子因子</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body">
                                <h3 class="text-success">${totalCombinations}</h3>
                                <p class="mb-0">可生成组合</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body">
                                <h3 class="text-warning">${denCount}</h3>
                                <p class="mb-0">分母因子</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <p class="text-muted">
                        将生成 <strong>${totalCombinations}</strong> 个组合因子
                        (每个分子因子与每个分母因子组合)
                    </p>
                </div>
            `;
        } else {
            previewContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calculator fa-3x mb-3"></i>
                    <p>添加分子和分母因子后，可以批量生成组合因子</p>
                    <small>每个分子因子将与每个分母因子进行组合</small>
                    <div class="mt-3">
                        <p class="mb-1">分子因子: <span class="badge bg-primary">${numCount}</span></p>
                        <p class="mb-0">分母因子: <span class="badge bg-warning">${denCount}</span></p>
                    </div>
                </div>
            `;
        }
    }

    async batchGenerateCombinations() {
        if (this.numeratorList.length === 0 || this.denominatorList.length === 0) {
            this.showError('请先添加分子和分母因子');
            return;
        }

        const batchCount = document.getElementById('batch-count-select').value;
        let numerators = [...this.numeratorList];
        let denominators = [...this.denominatorList];

        // 如果不是"全部"，则需要限制总组合数量
        if (batchCount !== 'all') {
            const limit = parseInt(batchCount);
            const totalPossible = numerators.length * denominators.length;

            if (totalPossible > limit) {
                // 计算最优的分子和分母数量组合
                const ratio = Math.sqrt(limit / totalPossible);
                const numLimit = Math.max(1, Math.floor(numerators.length * ratio));
                const denLimit = Math.max(1, Math.ceil(limit / numLimit));

                numerators = numerators.slice(0, numLimit);
                denominators = denominators.slice(0, denLimit);

                console.log(`限制组合数量到 ${limit}：使用 ${numLimit} 个分子 × ${denLimit} 个分母 = ${numLimit * denLimit} 个组合`);
            }
        }

        let generatedCount = 0;
        const totalCombinations = numerators.length * denominators.length;
        let processedCount = 0;

        // 显示进度
        this.showSuccessMessage(`开始生成 ${totalCombinations} 个组合因子...`);

        for (const numerator of numerators) {
            for (const denominator of denominators) {
                const combinationId = `${numerator.id}/${denominator.id}`;

                // 检查是否已存在
                if (!this.combinationFactors.find(f => f.id === combinationId)) {
                    const description = await this.generateAdvancedCombinationDescription(numerator, denominator);

                    const combinationFactor = {
                        id: combinationId,
                        description: description,
                        numerator: numerator,
                        denominator: denominator,
                        created_at: new Date().toISOString()
                    };

                    this.combinationFactors.push(combinationFactor);
                    generatedCount++;
                }

                processedCount++;

                // 每10个组合更新一次进度
                if (processedCount % 10 === 0 || processedCount === totalCombinations) {
                    this.showSuccessMessage(`生成进度: ${processedCount}/${totalCombinations} (${((processedCount/totalCombinations)*100).toFixed(1)}%)`);
                }
            }
        }

        this.updateCombinationButtons();
        this.showSuccessMessage(`批量生成完成！新增 ${generatedCount} 个组合因子，总计 ${this.combinationFactors.length} 个`);
    }

    previewCombinations() {
        if (this.numeratorList.length === 0 || this.denominatorList.length === 0) {
            this.showError('请先添加分子和分母因子');
            return;
        }

        const batchCount = document.getElementById('batch-count-select').value;
        let numerators = [...this.numeratorList];
        let denominators = [...this.denominatorList];

        // 如果不是"全部"，则需要限制总组合数量
        if (batchCount !== 'all') {
            const limit = parseInt(batchCount);
            const totalPossible = numerators.length * denominators.length;

            if (totalPossible > limit) {
                // 计算最优的分子和分母数量组合
                const ratio = Math.sqrt(limit / totalPossible);
                const numLimit = Math.max(1, Math.floor(numerators.length * ratio));
                const denLimit = Math.max(1, Math.ceil(limit / numLimit));

                numerators = numerators.slice(0, numLimit);
                denominators = denominators.slice(0, denLimit);
            }
        }

        let previewHtml = '<div class="modal fade" id="previewModal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content">';
        previewHtml += '<div class="modal-header"><h5 class="modal-title">组合因子预览</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div>';
        previewHtml += '<div class="modal-body" style="max-height: 400px; overflow-y: auto;">';

        // 显示已生成的组合因子及其描述
        if (this.combinationFactors.length > 0) {
            previewHtml += '<h6 class="text-primary mb-3">已生成的组合因子:</h6>';
            this.combinationFactors.forEach((factor, index) => {
                previewHtml += `
                    <div class="border-bottom py-3">
                        <div class="fw-bold text-success mb-1">${factor.id}</div>
                        <div class="text-muted small mb-2" style="line-height: 1.4;">
                            ${factor.description}
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-secondary">
                                <i class="fas fa-clock me-1"></i>
                                ${new Date(factor.created_at).toLocaleString()}
                            </small>
                            <button class="btn btn-outline-info btn-sm" onclick="app.translateFactorDescription('${factor.id}', ${index})">
                                <i class="fas fa-language me-1"></i>
                                翻译描述
                            </button>
                        </div>
                        <div id="translation-${index}" class="mt-2" style="display: none;">
                            <div class="alert alert-info small mb-0">
                                <strong>中文翻译:</strong>
                                <div id="translation-content-${index}">翻译中...</div>
                            </div>
                        </div>
                    </div>
                `;
            });
        } else {
            // 如果没有生成的组合因子，显示将要生成的预览
            previewHtml += '<h6 class="text-info mb-3">将要生成的组合预览:</h6>';
            let previewCount = 0;
            const maxPreview = 10; // 最多预览10个

            numerators.forEach((numerator, i) => {
                denominators.forEach((denominator, j) => {
                    if (previewCount < maxPreview) {
                        const combinationId = `${numerator.id}/${denominator.id}`;
                        previewHtml += `
                            <div class="border-bottom py-2">
                                <div class="fw-bold text-success">${combinationId}</div>
                                <small class="text-muted">${numerator.id} ÷ ${denominator.id}</small>
                            </div>
                        `;
                        previewCount++;
                    }
                });
            });

            if (numerators.length * denominators.length > maxPreview) {
                previewHtml += `
                    <div class="text-center py-2 text-muted">
                        <small>... 还有 ${numerators.length * denominators.length - maxPreview} 个组合将被生成</small>
                    </div>
                `;
            }
        }

        previewHtml += '</div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button></div>';
        previewHtml += '</div></div></div>';

        // 移除旧的模态框
        const oldModal = document.getElementById('previewModal');
        if (oldModal) oldModal.remove();

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', previewHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('previewModal'));
        modal.show();
    }

    async generateAdvancedCombinationDescription(numerator, denominator) {
        try {
            const response = await fetch('/api/generate_description', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    numerator_id: numerator.id,
                    numerator_description: numerator.description,
                    denominator_id: denominator.id,
                    denominator_description: denominator.description
                })
            });

            const data = await response.json();

            if (data.success) {
                console.log(`Generated description in ${data.generation_time}s`);
                return data.description;
            } else {
                console.error('Description generation failed:', data.error);
                // 回退到简单描述
                return this.generateSimpleCombinationDescription(numerator, denominator);
            }
        } catch (error) {
            console.error('Description generation error:', error);
            // 回退到简单描述
            return this.generateSimpleCombinationDescription(numerator, denominator);
        }
    }

    generateSimpleCombinationDescription(numerator, denominator) {
        // 简单的回退描述生成
        return `This ratio measures ${numerator.id} relative to ${denominator.id}. ` +
               `It provides insights into the relationship between these two financial metrics. ` +
               `This combination factor can be used for comparative analysis and performance evaluation.`;
    }

    generateCombinationDescription(numerator, denominator) {
        // 保持向后兼容
        return this.generateSimpleCombinationDescription(numerator, denominator);
    }

    extractKeywords(description) {
        // 简单的关键词提取
        const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
        const words = description.split(/\s+/).filter(word =>
            word.length > 2 && !commonWords.includes(word.toLowerCase())
        );
        return words.slice(0, 3); // 返回前3个关键词
    }

    clearAllCombinations() {
        this.numeratorList = [];
        this.denominatorList = [];
        this.combinationFactors = [];

        this.updateNumeratorList();
        this.updateDenominatorList();
        this.updateCombinationButtons();

        this.showSuccessMessage('已清空所有组合因子数据');
    }

    // 翻译因子描述
    async translateFactorDescription(factorId, index) {
        const translationDiv = document.getElementById(`translation-${index}`);
        const contentDiv = document.getElementById(`translation-content-${index}`);

        // 显示翻译区域
        translationDiv.style.display = 'block';
        contentDiv.innerHTML = '翻译中...';

        try {
            // 找到对应的因子
            const factor = this.combinationFactors.find(f => f.id === factorId);
            if (!factor) {
                contentDiv.innerHTML = '未找到因子信息';
                return;
            }

            // 调用翻译API
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    texts: [factor.description]  // 后端期望texts数组
                })
            });

            if (!response.ok) {
                throw new Error('翻译请求失败');
            }

            const data = await response.json();

            if (data.success && data.translations && data.translations.length > 0) {
                contentDiv.innerHTML = data.translations[0];
            } else {
                contentDiv.innerHTML = '翻译失败: ' + (data.error || '未知错误');
            }

        } catch (error) {
            console.error('翻译错误:', error);
            contentDiv.innerHTML = '翻译失败: ' + error.message;
        }
    }

    exportCombinationToTxt() {
        if (this.combinationFactors.length === 0) {
            this.showError('没有可导出的组合因子，请先生成组合因子');
            return;
        }

        let txtContent = '';

        this.combinationFactors.forEach((factor, index) => {
            if (index > 0) txtContent += '\n';

            // 只导出组合因子ID
            txtContent += factor.id;
        });

        // 创建并下载文件
        const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `combination_factors_${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccessMessage(`已导出 ${this.combinationFactors.length} 个组合因子到TXT文件`);
    }

    // ==================== 批量操作功能 ====================

    bindBatchOperationEvents() {
        // 选择按钮
        const selectAllBtn = document.getElementById('select-all-btn');
        const selectNoneBtn = document.getElementById('select-none-btn');
        const selectByScoreBtn = document.getElementById('select-by-score-btn');
        const selectTopBtn = document.getElementById('select-top-btn');
        const batchAddNumeratorBtn = document.getElementById('batch-add-numerator-btn');
        const batchAddDenominatorBtn = document.getElementById('batch-add-denominator-btn');

        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                this.selectAllFactors(true);
            });
        }

        if (selectNoneBtn) {
            selectNoneBtn.addEventListener('click', () => {
                this.selectAllFactors(false);
            });
        }

        if (selectByScoreBtn) {
            selectByScoreBtn.addEventListener('click', () => {
                this.selectFactorsByScore();
            });
        }

        if (selectTopBtn) {
            selectTopBtn.addEventListener('click', () => {
                this.selectTopFactors(10);
            });
        }

        if (batchAddNumeratorBtn) {
            batchAddNumeratorBtn.addEventListener('click', () => {
                this.batchAddToNumeratorList();
            });
        }

        if (batchAddDenominatorBtn) {
            batchAddDenominatorBtn.addEventListener('click', () => {
                this.batchAddToDenominatorList();
            });
        }

        // 监听复选框变化
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('factor-checkbox')) {
                this.updateBatchButtons();
            }
        });
    }

    selectAllFactors(select) {
        const checkboxes = document.querySelectorAll('.factor-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = select;
        });
        this.updateBatchButtons();
    }

    selectFactorsByScore() {
        const threshold = parseFloat(document.getElementById('score-threshold-select').value);
        const checkboxes = document.querySelectorAll('.factor-checkbox');
        let selectedCount = 0;

        checkboxes.forEach(checkbox => {
            const factorId = checkbox.getAttribute('data-factor-id');
            const factor = this.allResults.find(result => result.id === factorId);

            if (factor && factor.overall_score >= threshold) {
                checkbox.checked = true;
                selectedCount++;
            } else {
                checkbox.checked = false;
            }
        });

        this.updateBatchButtons();

        const thresholdPercent = (threshold * 100).toFixed(0);
        if (threshold > 0) {
            this.showSuccessMessage(`已选择 ${selectedCount} 个得分≥${thresholdPercent}% 的因子`);
        } else {
            this.showSuccessMessage(`已选择所有 ${selectedCount} 个因子`);
        }
    }

    selectTopFactors(count) {
        const checkboxes = document.querySelectorAll('.factor-checkbox');

        // 获取当前显示的因子并按得分排序
        const visibleFactors = [];
        checkboxes.forEach(checkbox => {
            const factorId = checkbox.getAttribute('data-factor-id');
            const factor = this.allResults.find(result => result.id === factorId);
            if (factor) {
                visibleFactors.push({ factor, checkbox });
            }
        });

        // 按得分降序排序
        visibleFactors.sort((a, b) => b.factor.overall_score - a.factor.overall_score);

        // 选择前N个
        visibleFactors.forEach((item, index) => {
            item.checkbox.checked = index < count;
        });

        this.updateBatchButtons();

        const actualCount = Math.min(count, visibleFactors.length);
        this.showSuccessMessage(`已选择得分最高的 ${actualCount} 个因子`);
    }

    updateBatchButtons() {
        const selectedCheckboxes = document.querySelectorAll('.factor-checkbox:checked');
        const hasSelection = selectedCheckboxes.length > 0;

        const batchAddNumeratorBtn = document.getElementById('batch-add-numerator-btn');
        const batchAddDenominatorBtn = document.getElementById('batch-add-denominator-btn');

        if (batchAddNumeratorBtn) {
            batchAddNumeratorBtn.disabled = !hasSelection;
            batchAddNumeratorBtn.innerHTML = hasSelection ?
                `<i class="fas fa-arrow-up me-1"></i>批量分子 (${selectedCheckboxes.length})` :
                `<i class="fas fa-arrow-up me-1"></i>批量分子`;
        }

        if (batchAddDenominatorBtn) {
            batchAddDenominatorBtn.disabled = !hasSelection;
            batchAddDenominatorBtn.innerHTML = hasSelection ?
                `<i class="fas fa-arrow-down me-1"></i>批量分母 (${selectedCheckboxes.length})` :
                `<i class="fas fa-arrow-down me-1"></i>批量分母`;
        }
    }

    batchAddToNumeratorList() {
        const selectedCheckboxes = document.querySelectorAll('.factor-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            this.showError('请先选择要添加的因子');
            return;
        }

        let addedCount = 0;
        let skippedCount = 0;

        selectedCheckboxes.forEach(checkbox => {
            const factorId = checkbox.getAttribute('data-factor-id');
            const factor = this.allResults.find(result => result.id === factorId);

            if (factor && !this.numeratorList.find(f => f.id === factorId)) {
                this.numeratorList.push(factor);
                addedCount++;
            } else {
                skippedCount++;
            }
        });

        this.updateNumeratorList();
        this.updateCombinationButtons();

        // 取消选择
        selectedCheckboxes.forEach(checkbox => checkbox.checked = false);
        this.updateBatchButtons();

        let message = `已添加 ${addedCount} 个因子到分子列表`;
        if (skippedCount > 0) {
            message += `，跳过 ${skippedCount} 个重复因子`;
        }
        this.showSuccessMessage(message);
    }

    batchAddToDenominatorList() {
        const selectedCheckboxes = document.querySelectorAll('.factor-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            this.showError('请先选择要添加的因子');
            return;
        }

        let addedCount = 0;
        let skippedCount = 0;

        selectedCheckboxes.forEach(checkbox => {
            const factorId = checkbox.getAttribute('data-factor-id');
            const factor = this.allResults.find(result => result.id === factorId);

            if (factor && !this.denominatorList.find(f => f.id === factorId)) {
                this.denominatorList.push(factor);
                addedCount++;
            } else {
                skippedCount++;
            }
        });

        this.updateDenominatorList();
        this.updateCombinationButtons();

        // 取消选择
        selectedCheckboxes.forEach(checkbox => checkbox.checked = false);
        this.updateBatchButtons();

        let message = `已添加 ${addedCount} 个因子到分母列表`;
        if (skippedCount > 0) {
            message += `，跳过 ${skippedCount} 个重复因子`;
        }
        this.showSuccessMessage(message);
    }

    updateScoreThresholdOptions() {
        const scoreSelect = document.getElementById('score-threshold-select');
        if (!scoreSelect || !this.allResults || this.allResults.length === 0) return;

        // 计算得分分布
        const scores = this.allResults.map(result => result.overall_score);
        const maxScore = Math.max(...scores);
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;

        // 计算不同阈值下的因子数量
        const thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95];
        const counts = thresholds.map(threshold =>
            scores.filter(score => score >= threshold).length
        );

        // 更新选项文本
        const options = scoreSelect.querySelectorAll('option');
        options.forEach((option, index) => {
            if (index === 0) {
                option.textContent = `全部 (${this.allResults.length})`;
            } else if (index <= thresholds.length) {
                const threshold = thresholds[index - 1];
                const count = counts[index - 1];
                const percent = (threshold * 100).toFixed(0);
                option.textContent = `≥${percent}% (${count})`;
            }
        });

        // 添加动态推荐选项
        const avgPercent = (avgScore * 100).toFixed(0);
        const avgCount = scores.filter(score => score >= avgScore).length;

        // 如果平均分选项不存在，添加它
        const avgValue = (Math.round(avgScore * 10) / 10).toString();
        let avgOption = scoreSelect.querySelector(`option[value="${avgValue}"]`);
        if (!avgOption && avgScore > 0.2 && avgScore < 0.95) {
            avgOption = document.createElement('option');
            avgOption.value = avgValue;
            avgOption.textContent = `≥${avgPercent}% 平均分 (${avgCount})`;
            avgOption.style.backgroundColor = '#e3f2fd';

            // 插入到合适位置
            const options = Array.from(scoreSelect.options);
            let inserted = false;
            for (let i = 1; i < options.length; i++) {
                if (parseFloat(options[i].value) > avgScore) {
                    scoreSelect.insertBefore(avgOption, options[i]);
                    inserted = true;
                    break;
                }
            }
            if (!inserted) {
                scoreSelect.appendChild(avgOption);
            }
        }
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new BERTSearchApp();
});

// 全局测试函数
window.testFilters = function() {
    console.log('Testing filters...');
    if (app && app.allResults && app.allResults.length > 0) {
        console.log('Total results:', app.allResults.length);
        app.applyFilters();
    } else {
        console.log('No results to filter');
    }
};
