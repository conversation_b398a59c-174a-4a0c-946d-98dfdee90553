// BERT融合搜索引擎 - 前端交互逻辑

class BERTSearchApp {
    constructor() {
        this.currentQuery = '';
        this.isSearching = false;
        this.allResults = [];
        this.displayedResults = 0;
        this.resultsPerPage = 50;  // 增加每页显示数量
        this.isTranslated = false;  // 翻译状态
        this.isTranslating = false;  // 翻译进行中
        this.filterOptions = null;  // 筛选选项
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkSystemStatus();
        this.showInitializationProgress();
        this.loadFilterOptions();
    }

    bindEvents() {
        try {
            // 搜索按钮事件
            const searchBtn = document.getElementById('search-btn');
            if (searchBtn) {
                searchBtn.addEventListener('click', () => {
                    console.log('🔍 Search button clicked');
                    this.performSearch();
                });
                console.log('✅ Search button event bound');

                // 测试按钮是否可点击
                searchBtn.style.cursor = 'pointer';
            } else {
                console.error('❌ Search button not found');
            }

            // 搜索输入框回车事件
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });
                console.log('✅ Search input event bound');
            } else {
                console.error('❌ Search input not found');
            }

            // 快速查询按钮事件
            document.querySelectorAll('.quick-query').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const query = e.target.getAttribute('data-query');
                    document.getElementById('search-input').value = query;
                    this.performSearch();
                });
            });

            // 阈值调整按钮事件
            const moreResultsBtn = document.getElementById('more-results-btn');
            if (moreResultsBtn) {
                moreResultsBtn.addEventListener('click', () => {
                    this.adjustThreshold('more_results');
                });
            }

            const morePreciseBtn = document.getElementById('more-precise-btn');
            if (morePreciseBtn) {
                morePreciseBtn.addEventListener('click', () => {
                    this.adjustThreshold('more_precise');
                });
            }

            const resetBtn = document.getElementById('reset-btn');
            if (resetBtn) {
                resetBtn.addEventListener('click', () => {
                    this.adjustThreshold('reset');
                });
            }

            // 数据集加载按钮事件
            const loadDatasetBtn = document.getElementById('load-dataset-btn');
            if (loadDatasetBtn) {
                loadDatasetBtn.addEventListener('click', () => {
                    console.log('📊 Load dataset button clicked');
                    this.loadDataset();
                });
                console.log('✅ Load dataset button event bound');

                // 测试按钮是否可点击
                loadDatasetBtn.style.cursor = 'pointer';
            } else {
                console.error('❌ Load dataset button not found');
            }

            // 筛选相关事件
            const categorySelect = document.getElementById('category-select');
            if (categorySelect) {
                categorySelect.addEventListener('change', () => {
                    this.updateSubcategoryOptions();
                });
                console.log('✅ Category select event bound');
            }

            const clearFiltersBtn = document.getElementById('clear-filters-btn');
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', () => {
                    this.clearFilters();
                });
                console.log('✅ Clear filters button event bound');
            }

        } catch (error) {
            console.error('❌ Error binding events:', error);
        }
    }

    async checkSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.success) {
                const initStatus = data.initialization_status;

                if (initStatus.status === 'completed' && data.data_loaded) {
                    this.updateStatusIndicator('ready', '系统就绪');
                    this.showWelcomeMessage();
                } else if (initStatus.status === 'initializing') {
                    this.updateStatusIndicator('loading', `初始化中... ${initStatus.progress}%`);
                    this.showInitializationProgress(initStatus);
                    // 继续检查状态
                    setTimeout(() => this.checkSystemStatus(), 2000);
                } else if (initStatus.status === 'error') {
                    this.updateStatusIndicator('error', '初始化失败');
                    this.showError(initStatus.message);
                } else {
                    this.updateStatusIndicator('loading', '准备初始化...');
                    setTimeout(() => this.checkSystemStatus(), 1000);
                }
            } else {
                this.updateStatusIndicator('error', '系统未就绪');
            }
        } catch (error) {
            console.error('Status check failed:', error);
            this.updateStatusIndicator('error', '连接失败');
        }
    }

    updateStatusIndicator(status, message) {
        const indicator = document.getElementById('status-indicator');
        const iconClass = status === 'ready' ? 'text-success' : 
                         status === 'loading' ? 'text-warning' : 'text-danger';
        
        indicator.innerHTML = `
            <i class="fas fa-circle ${iconClass} me-1"></i>
            ${message}
        `;
    }

    showInitializationProgress(initStatus = null) {
        const resultsContainer = document.getElementById('search-results');

        if (initStatus && initStatus.status === 'initializing') {
            resultsContainer.innerHTML = `
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-cog fa-spin text-primary mb-3" style="font-size: 4rem;"></i>
                        <h4 class="card-title">正在初始化BERT融合搜索引擎</h4>
                        <p class="card-text text-muted mb-4">${initStatus.message}</p>

                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                 style="width: ${initStatus.progress}%"
                                 aria-valuenow="${initStatus.progress}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                ${initStatus.progress}%
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <i class="fas fa-download text-info mb-2" style="font-size: 1.5rem;"></i>
                                <h6>模型预加载</h6>
                                <small class="text-muted">预加载BERT模型到内存</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-database text-success mb-2" style="font-size: 1.5rem;"></i>
                                <h6>向量缓存</h6>
                                <small class="text-muted">构建高速向量缓存系统</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-search text-warning mb-2" style="font-size: 1.5rem;"></i>
                                <h6>索引构建</h6>
                                <small class="text-muted">构建优化的搜索索引</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            resultsContainer.innerHTML = `
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-hourglass-half text-primary mb-3" style="font-size: 4rem;"></i>
                        <h4 class="card-title">系统准备中...</h4>
                        <p class="card-text text-muted">
                            正在启动BERT融合搜索引擎，请稍候...
                        </p>
                    </div>
                </div>
            `;
        }
    }

    showWelcomeMessage() {
        const resultsContainer = document.getElementById('search-results');
        resultsContainer.innerHTML = `
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-rocket text-primary mb-3" style="font-size: 4rem;"></i>
                    <h4 class="card-title">🎉 BERT融合搜索引擎已就绪</h4>
                    <p class="card-text text-muted">
                        ⚡ 优化版本：模型预加载 + 向量缓存 + 索引持久化
                    </p>
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <i class="fas fa-brain text-info mb-2" style="font-size: 2rem;"></i>
                            <h6>BERT多模型融合</h6>
                            <small class="text-muted">预加载的深度学习模型</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-tachometer-alt text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>高速缓存</h6>
                            <small class="text-muted">向量缓存 + 索引持久化</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-sliders-h text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>动态阈值</h6>
                            <small class="text-muted">智能阈值调整</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-language text-danger mb-2" style="font-size: 2rem;"></i>
                            <h6>中英文支持</h6>
                            <small class="text-muted">跨语言语义理解</small>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-outline-primary me-2" onclick="this.showPerformanceStats()">
                            <i class="fas fa-chart-line me-1"></i>
                            查看性能统计
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async showPerformanceStats() {
        try {
            const response = await fetch('/api/performance');
            const data = await response.json();

            if (data.success) {
                const stats = data.performance_stats;
                const resultsContainer = document.getElementById('search-results');

                resultsContainer.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                系统性能统计
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>搜索性能</h6>
                                    <ul class="list-unstyled">
                                        <li>总搜索次数: ${stats.search_stats.total_searches}</li>
                                        <li>平均搜索时间: ${stats.search_stats.avg_search_time.toFixed(3)}s</li>
                                        <li>索引构建时间: ${stats.search_stats.index_build_time.toFixed(2)}s</li>
                                        <li>已索引文档: ${stats.indexed_documents}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>缓存性能</h6>
                                    <ul class="list-unstyled">
                                        <li>缓存命中率: ${(stats.model_stats.cache_hit_rate * 100).toFixed(1)}%</li>
                                        <li>总请求数: ${stats.model_stats.total_requests}</li>
                                        <li>平均编码时间: ${stats.model_stats.avg_encoding_time.toFixed(3)}s</li>
                                        <li>已加载模型: ${stats.model_stats.loaded_models.join(', ')}</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>缓存大小</h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <small class="text-muted">向量缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.vector_cache.toFixed(1)} MB</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">索引缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.index_cache.toFixed(1)} MB</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">模型缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.model_cache.toFixed(1)} MB</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">总缓存</small>
                                            <div class="fw-bold">${stats.cache_sizes.total_cache.toFixed(1)} MB</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary" onclick="location.reload()">
                                    <i class="fas fa-home me-1"></i>
                                    返回首页
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Failed to load performance stats:', error);
        }
    }

    async performSearch() {
        console.log('🔍 performSearch called');

        if (this.isSearching) {
            console.log('⏳ Search already in progress');
            return;
        }

        const searchInput = document.getElementById('search-input');
        if (!searchInput) {
            console.error('❌ Search input not found');
            this.showError('搜索输入框未找到');
            return;
        }

        const query = searchInput.value.trim();
        console.log('📝 Query:', query);

        if (!query) {
            this.showError('请输入搜索查询');
            return;
        }

        this.currentQuery = query;
        this.isSearching = true;
        this.showLoading();
        this.updateStatusIndicator('loading', '搜索中...');

        // 获取筛选条件
        const category = document.getElementById('category-select').value;
        const subcategory = document.getElementById('subcategory-select').value;
        const datasetName = document.getElementById('dataset-name-select').value;

        try {
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    category: category,
                    subcategory: subcategory,
                    dataset_name: datasetName
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
                this.updateThresholdStatus(data.session_stats);
                this.updateSearchStats(data);
            } else {
                this.showError(data.error || '搜索失败');
            }
        } catch (error) {
            console.error('Search failed:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateStatusIndicator('ready', '系统就绪');
        }
    }

    async adjustThreshold(action) {
        if (this.isSearching) return;

        this.isSearching = true;
        this.showLoading();
        this.updateStatusIndicator('loading', '调整阈值中...');

        try {
            const response = await fetch('/api/adjust_threshold', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: action })
            });

            const data = await response.json();

            if (data.success) {
                this.updateThresholdStatus(data.session_stats);
                
                if (data.results && data.results.length > 0) {
                    // 如果有重新搜索的结果，显示它们
                    this.displayResults({
                        results: data.results,
                        search_time: 0,
                        total_results: data.results.length
                    });
                }

                // 显示调整提示
                this.showAdjustmentMessage(action);
            } else {
                this.showError(data.error || '阈值调整失败');
            }
        } catch (error) {
            console.error('Threshold adjustment failed:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateStatusIndicator('ready', '系统就绪');
        }
    }

    showAdjustmentMessage(action) {
        const messages = {
            'more_results': '已降低阈值，获得更多结果',
            'more_precise': '已提高阈值，获得更精准结果',
            'reset': '已重置阈值到默认值'
        };

        const message = messages[action] || '阈值已调整';
        
        // 创建临时提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alert, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }

    displayResults(data) {
        const overviewContainer = document.getElementById('results-overview');
        const detailsContainer = document.getElementById('result-details');

        if (!data.results || data.results.length === 0) {
            overviewContainer.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5>未找到匹配结果</h5>
                        <p class="text-muted">尝试使用不同的关键词，或点击"更多结果"降低搜索阈值</p>
                    </div>
                </div>
            `;

            detailsContainer.innerHTML = `
                <div class="card">
                    <div class="card-body text-center text-muted">
                        <i class="fas fa-mouse-pointer mb-3" style="font-size: 3rem;"></i>
                        <h5>点击左侧因子查看详细信息</h5>
                        <p>选择一个因子以查看完整的描述、分类信息和匹配详情</p>
                    </div>
                </div>
            `;
            return;
        }

        // 保存所有结果
        this.allResults = data.results;
        this.displayedResults = 0;

        // 显示结果总览
        let overviewHtml = `
            <div class="card">
                <div class="card-header">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-list-ul me-2 text-primary"></i>
                            搜索结果 (${data.results.length} 条)
                        </h5>
                        <small class="text-muted">查询: "${this.currentQuery}" | 点击 <i class="fas fa-language text-primary"></i> 翻译单个因子</small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="results-list">
                    </div>
                    ${data.results.length > this.resultsPerPage ? `
                    <div class="card-footer text-center">
                        <button class="btn btn-outline-primary" id="load-more-btn">
                            <i class="fas fa-chevron-down me-1"></i>
                            加载更多结果
                        </button>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;

        overviewContainer.innerHTML = overviewHtml;

        // 增加每页显示数量
        this.resultsPerPage = 50;  // 从20增加到50

        // 加载第一页结果
        this.loadMoreResults();

        // 绑定加载更多按钮事件
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreResults();
            });
        }

        // 默认显示第一个结果的详情
        if (data.results.length > 0) {
            this.showResultDetails(data.results[0], 1);
        }
    }

    renderResults() {
        // 重新渲染所有结果（用于翻译切换）
        const resultsList = document.getElementById('results-list');
        if (!resultsList) return;

        resultsList.innerHTML = '';
        this.displayedResults = 0;

        // 恢复正常的分页大小
        this.resultsPerPage = 50;
        this.loadMoreResults();

        // 重新显示当前选中的结果详情
        const activeItem = document.querySelector('.result-overview-item.active');
        if (activeItem) {
            const index = parseInt(activeItem.dataset.index);
            this.showResultDetails(this.allResults[index], index + 1);
        }
    }

    loadMoreResults() {
        const resultsList = document.getElementById('results-list');
        const loadMoreBtn = document.getElementById('load-more-btn');

        const startIndex = this.displayedResults;
        const endIndex = Math.min(startIndex + this.resultsPerPage, this.allResults.length);

        for (let i = startIndex; i < endIndex; i++) {
            const result = this.allResults[i];
            const index = i;
            const scoreColor = result.overall_score > 0.7 ? 'success' :
                              result.overall_score > 0.4 ? 'warning' : 'secondary';

            const itemHtml = `
                <div class="list-group-item list-group-item-action result-overview-item compact-item"
                     data-index="${index}"
                     style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1 me-2">
                            <div class="fw-bold text-primary small">${result.id}</div>
                            <div class="text-muted" style="font-size: 0.75rem; line-height: 1.2;">
                                ${result.dataset_name_translated || result.dataset_name}
                            </div>
                        </div>
                        <div class="text-end d-flex align-items-center">
                            <button class="btn btn-outline-primary btn-sm me-2 translate-single-btn"
                                    data-index="${index}"
                                    style="font-size: 0.7rem; padding: 0.2rem 0.4rem;">
                                <i class="fas fa-language"></i>
                            </button>
                            <div>
                                <span class="badge bg-${scoreColor}" style="font-size: 0.7rem;">
                                    ${(result.overall_score * 100).toFixed(0)}%
                                </span>
                                <div class="text-muted" style="font-size: 0.65rem;">#${index + 1}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            resultsList.insertAdjacentHTML('beforeend', itemHtml);
        }

        this.displayedResults = endIndex;

        // 绑定新添加项目的点击事件
        const newItems = resultsList.querySelectorAll('.result-overview-item:not(.bound)');
        newItems.forEach(item => {
            item.classList.add('bound');
            item.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.showResultDetails(this.allResults[index], index + 1);

                // 更新选中状态
                document.querySelectorAll('.result-overview-item').forEach(i => i.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // 绑定翻译按钮事件
        const newTranslateButtons = resultsList.querySelectorAll('.translate-single-btn:not(.bound)');
        newTranslateButtons.forEach(button => {
            button.classList.add('bound');
            button.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止触发父元素的点击事件
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.translateSingleResult(index);
            });
        });

        // 更新加载更多按钮
        if (loadMoreBtn) {
            if (this.displayedResults >= this.allResults.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.innerHTML = `
                    <i class="fas fa-chevron-down me-1"></i>
                    加载更多结果 (${this.displayedResults}/${this.allResults.length})
                `;
            }
        }

        // 添加动画效果
        newItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('fade-in-up');
            }, index * 50);
        });

        // 如果是第一次加载，显示第一个结果的详情
        if (startIndex === 0 && this.allResults.length > 0) {
            this.showResultDetails(this.allResults[0], 1);
            document.querySelector('.result-overview-item').classList.add('active');
        }
    }

    showResultDetails(result, rank) {
        const detailsContainer = document.getElementById('result-details');

        const scoreColor = result.overall_score > 0.7 ? 'success' :
                          result.overall_score > 0.4 ? 'warning' : 'secondary';

        const detailsHtml = `
            <div class="card">
                <div class="card-header bg-${scoreColor} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">${result.id_translated || result.id}</h5>
                            <small class="opacity-75">${result.id}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-light text-dark fs-6">排名 #${rank}</span>
                            <div class="mt-1">
                                <small>综合得分: ${(result.overall_score * 100).toFixed(1)}%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 因子描述 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-info-circle me-2"></i>
                            因子描述
                        </h6>
                        <div class="bg-light p-3 rounded">
                            ${result.showTranslation && result.description_translated ?
                                `<p class="mb-2"><strong>中文:</strong> ${result.description_translated}</p>
                                 <p class="mb-0 small text-muted"><strong>原文:</strong> ${result.description}</p>` :
                                `<p class="mb-0">${result.description}</p>`
                            }
                        </div>
                    </div>

                    <!-- 分类信息 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-tags me-2"></i>
                            分类信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>数据集:</strong><br>
                                    <span class="text-primary">${result.dataset_name_translated || result.dataset_name}</span>
                                    ${result.dataset_name_translated ? `<br><small class="text-muted">${result.dataset_name}</small>` : ''}
                                </div>
                                <div class="mb-2">
                                    <strong>主类别:</strong><br>
                                    <span class="text-success">${result.category_name_translated || result.category_name}</span>
                                    ${result.category_name_translated ? `<br><small class="text-muted">${result.category_name}</small>` : ''}
                                </div>
                                <div class="mb-2">
                                    <strong>地区:</strong><br>
                                    <span class="text-warning">${result.region || 'N/A'}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>子类别:</strong><br>
                                    <span class="text-info">${result.subcategory_name_translated || result.subcategory_name}</span>
                                    ${result.subcategory_name_translated ? `<br><small class="text-muted">${result.subcategory_name}</small>` : ''}
                                </div>
                                <div class="mb-2">
                                    <strong>数据延迟:</strong><br>
                                    <span class="text-secondary">${result.delay || 'N/A'}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>数据范围:</strong><br>
                                    <span class="text-muted">${result.universe || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 评分信息 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-chart-bar me-2"></i>
                            评分详情
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <strong>综合得分</strong>
                                    </div>
                                    <span class="badge bg-${scoreColor} fs-6">${(result.overall_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <strong>语义得分</strong>
                                    </div>
                                    <span class="badge bg-info fs-6">${(result.semantic_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <strong>关键词得分</strong>
                                    </div>
                                    <span class="badge bg-success fs-6">${(result.keyword_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 匹配信息 -->
                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-search me-2"></i>
                            匹配信息
                        </h6>

                        ${result.matched_columns.length > 0 ? `
                        <div class="mb-3">
                            <strong>匹配字段:</strong>
                            <div class="mt-2">
                                ${result.matched_columns.map(col =>
                                    `<span class="badge bg-primary me-1 mb-1">${col}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${result.matched_keywords.length > 0 ? `
                        <div>
                            <strong>匹配关键词:</strong>
                            <div class="mt-2">
                                ${result.matched_keywords.map(keyword =>
                                    `<span class="badge bg-success me-1 mb-1">${keyword}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        detailsContainer.innerHTML = detailsHtml;
        detailsContainer.classList.add('fade-in-up');
    }



    highlightKeywords(text, keywords) {
        if (!keywords || keywords.length === 0) return text;
        
        let highlightedText = text;
        keywords.forEach(keyword => {
            const regex = new RegExp(`(${keyword})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    updateThresholdStatus(stats) {
        if (!stats || !stats.current_thresholds) return;

        const thresholds = stats.current_thresholds;
        
        document.getElementById('min-threshold').textContent = thresholds.min_score.toFixed(3);
        document.getElementById('semantic-threshold').textContent = thresholds.semantic.toFixed(3);
        document.getElementById('keyword-threshold').textContent = thresholds.keyword.toFixed(3);
        document.getElementById('preference-score').textContent = stats.preference_score.toFixed(2);

        document.getElementById('threshold-status').style.display = 'block';
    }

    updateSearchStats(data) {
        document.getElementById('search-time').textContent = `${data.search_time}s`;
        document.getElementById('result-count').textContent = data.total_results;
        
        if (data.session_stats) {
            document.getElementById('total-searches').textContent = data.session_stats.total_searches;
            document.getElementById('threshold-trend').textContent = data.session_stats.threshold_trend;
        }

        document.getElementById('search-stats').style.display = 'block';
    }

    showLoading() {
        document.getElementById('loading-indicator').style.display = 'block';
        document.getElementById('search-btn').disabled = true;
        document.getElementById('search-btn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>搜索中...';
    }

    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
        document.getElementById('search-btn').disabled = false;
        document.getElementById('search-btn').innerHTML = '<i class="fas fa-search me-1"></i>搜索';
    }

    showError(message) {
        const overviewContainer = document.getElementById('results-overview');
        overviewContainer.innerHTML = `
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle text-danger mb-3" style="font-size: 3rem;"></i>
                    <h5>错误</h5>
                    <p class="text-muted">${message}</p>
                </div>
            </div>
        `;
    }

    async loadDataset() {
        console.log('📊 loadDataset called');

        const selectElement = document.getElementById('dataset-select');
        if (!selectElement) {
            console.error('❌ Dataset select not found');
            alert('数据集选择器未找到');
            return;
        }

        const filename = selectElement.value;
        console.log('📁 Selected filename:', filename);

        if (!filename) {
            alert('请先选择一个数据集');
            return;
        }

        const loadBtn = document.getElementById('load-dataset-btn');
        const originalText = loadBtn.innerHTML;

        try {
            // 显示加载状态
            loadBtn.disabled = true;
            loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';

            this.updateStatusIndicator('loading', '正在加载数据集...');

            const response = await fetch('/api/load_dataset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filename: filename })
            });

            const data = await response.json();

            if (data.success) {
                // 更新数据集信息显示
                const selectedOption = selectElement.options[selectElement.selectedIndex];
                document.getElementById('dataset-info').textContent =
                    `当前: ${selectedOption.text} (${data.sample_size}/${data.total_records} 条记录)`;

                this.updateStatusIndicator('ready', '数据集加载完成');

                // 清空搜索结果
                document.getElementById('results-overview').innerHTML = '';
                document.getElementById('result-details').innerHTML = `
                    <div class="card">
                        <div class="card-body text-center text-muted">
                            <i class="fas fa-database mb-3 text-success" style="font-size: 3rem;"></i>
                            <h5>数据集已更新</h5>
                            <p>新数据集已加载完成，现在可以开始搜索了</p>
                        </div>
                    </div>
                `;

                // 显示成功提示
                this.showSuccessMessage(`成功加载数据集: ${selectedOption.text}`);

            } else {
                this.updateStatusIndicator('error', '数据集加载失败');
                this.showError(data.error || '数据集加载失败');
            }

        } catch (error) {
            console.error('Dataset loading failed:', error);
            this.updateStatusIndicator('error', '连接失败');
            this.showError('网络错误，请重试');
        } finally {
            loadBtn.disabled = false;
            loadBtn.innerHTML = originalText;
        }
    }

    showSuccessMessage(message) {
        // 创建临时成功提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container');
        container.insertBefore(alert, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }

    async translateSingleResult(index) {
        const result = this.allResults[index];
        const translateBtn = document.querySelector(`[data-index="${index}"].translate-single-btn`);

        if (!result || !translateBtn) return;

        // 如果已经翻译过，切换显示状态
        if (result.description_translated) {
            result.showTranslation = !result.showTranslation;
            this.updateSingleResultDisplay(index);
            this.updateTranslateButtonState(translateBtn, result);
            return;
        }

        // 执行翻译
        const originalHtml = translateBtn.innerHTML;

        try {
            translateBtn.disabled = true;
            translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    texts: [result.description]
                })
            });

            const data = await response.json();

            if (data.success && data.translations.length > 0) {
                // 保存翻译结果
                result.description_translated = data.translations[0];
                result.showTranslation = true;

                // 更新显示
                this.updateSingleResultDisplay(index);
                this.updateTranslateButtonState(translateBtn, result);

                // 如果当前选中的是这个结果，更新详情显示
                const activeItem = document.querySelector('.result-overview-item.active');
                if (activeItem && parseInt(activeItem.dataset.index) === index) {
                    this.showResultDetails(result, index + 1);
                }

                this.showSuccessMessage(`翻译完成！用时 ${data.translation_time}s`);
            } else {
                this.showError(data.error || '翻译失败');
            }

        } catch (error) {
            console.error('Translation failed:', error);
            this.showError('翻译失败，请重试');
        } finally {
            translateBtn.disabled = false;
            if (!result.description_translated) {
                translateBtn.innerHTML = originalHtml;
            }
        }
    }

    updateTranslateButtonState(button, result) {
        if (result.description_translated) {
            if (result.showTranslation) {
                button.innerHTML = '<i class="fas fa-eye-slash"></i>';
                button.className = 'btn btn-outline-secondary btn-sm me-2 translate-single-btn';
                button.title = '显示原文';
            } else {
                button.innerHTML = '<i class="fas fa-language"></i>';
                button.className = 'btn btn-outline-success btn-sm me-2 translate-single-btn';
                button.title = '显示翻译';
            }
        } else {
            button.innerHTML = '<i class="fas fa-language"></i>';
            button.className = 'btn btn-outline-primary btn-sm me-2 translate-single-btn';
            button.title = '翻译';
        }
    }

    updateSingleResultDisplay(index) {
        // 这个方法可以用来更新单个结果的显示状态
        // 目前主要通过详情面板显示翻译，所以这里暂时不需要特殊处理
    }
}

// 初始化应用
    async loadFilterOptions() {
        try {
            const response = await fetch('/api/get_filter_options');
            const data = await response.json();

            if (data.success) {
                this.filterOptions = data;
                this.populateFilterSelects();
            } else {
                console.error('Failed to load filter options:', data.error);
            }
        } catch (error) {
            console.error('Failed to load filter options:', error);
        }
    }

    populateFilterSelects() {
        if (!this.filterOptions) return;

        // 填充数据源选择器
        const datasetSelect = document.getElementById('dataset-name-select');
        datasetSelect.innerHTML = '<option value="">所有数据源</option>';

        this.filterOptions.dataset_names.forEach(dataset => {
            const option = document.createElement('option');
            option.value = dataset;
            option.textContent = dataset;
            datasetSelect.appendChild(option);
        });
    }

    updateSubcategoryOptions() {
        if (!this.filterOptions) return;

        const categorySelect = document.getElementById('category-select');
        const subcategorySelect = document.getElementById('subcategory-select');

        const selectedCategory = categorySelect.value;

        // 清空子类别选择器
        subcategorySelect.innerHTML = '<option value="">所有子类别</option>';

        if (selectedCategory && this.filterOptions.category_subcategory_map[selectedCategory]) {
            const subcategories = this.filterOptions.category_subcategory_map[selectedCategory];
            subcategories.forEach(subcategory => {
                const option = document.createElement('option');
                option.value = subcategory;
                option.textContent = subcategory;
                subcategorySelect.appendChild(option);
            });
        }
    }

    clearFilters() {
        document.getElementById('category-select').value = '';
        document.getElementById('subcategory-select').value = '';
        document.getElementById('dataset-name-select').value = '';

        // 重置子类别选项
        this.updateSubcategoryOptions();

        // 如果有当前查询，重新搜索
        if (this.currentQuery) {
            this.performSearch();
        }
    }
}

let app;
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM loaded, initializing app...');

    // 检查关键元素是否存在
    const searchBtn = document.getElementById('search-btn');
    const loadBtn = document.getElementById('load-dataset-btn');
    const searchInput = document.getElementById('search-input');

    console.log('🔍 Element check:');
    console.log('  Search button:', searchBtn ? '✅ Found' : '❌ Missing');
    console.log('  Load button:', loadBtn ? '✅ Found' : '❌ Missing');
    console.log('  Search input:', searchInput ? '✅ Found' : '❌ Missing');

    try {
        app = new BERTSearchApp();
        console.log('✅ App initialized successfully');
    } catch (error) {
        console.error('❌ App initialization failed:', error);
    }
});
