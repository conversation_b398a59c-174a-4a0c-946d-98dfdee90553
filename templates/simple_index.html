<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版 - BERT融合搜索引擎</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-search me-2"></i>
                简化版 BERT融合搜索引擎
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-circle text-success me-1"></i>
                    系统就绪
                </span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 数据集选择 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据集选择
                </h6>
            </div>
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <select class="form-select" id="dataset-select">
                            <option value="">请选择数据集...</option>
                            {% for file in data_files %}
                            <option value="{{ file.filename }}"
                                    {% if file.filename == 'USA_1_TOP3000.csv' %}selected{% endif %}>
                                {{ file.display_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info w-100" id="load-dataset-btn">
                            <i class="fas fa-upload me-1"></i>
                            加载数据集
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    因子搜索
                </h6>
            </div>
            <div class="card-body">
                <!-- 类型筛选 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label small text-muted">主类别</label>
                        <select class="form-select" id="category-select">
                            <option value="">所有主类别</option>
                            <option value="Analyst">分析师 (Analyst)</option>
                            <option value="Fundamental">基本面 (Fundamental)</option>
                            <option value="Model">模型 (Model)</option>
                            <option value="News">新闻 (News)</option>
                            <option value="Option">期权 (Option)</option>
                            <option value="Price Volume">价量 (Price Volume)</option>
                            <option value="Risk">风险 (Risk)</option>
                            <option value="Sentiment">情绪 (Sentiment)</option>
                            <option value="Short Interest">做空 (Short Interest)</option>
                            <option value="Social Media">社交媒体 (Social Media)</option>
                            <option value="Other">其他 (Other)</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label small text-muted">子类别</label>
                        <select class="form-select" id="subcategory-select">
                            <option value="">所有子类别</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label small text-muted">数据源</label>
                        <select class="form-select" id="dataset-name-select">
                            <option value="">所有数据源</option>
                        </select>
                    </div>
                </div>
                
                <!-- 搜索输入 -->
                <div class="input-group mb-3">
                    <input type="text" 
                           class="form-control form-control-lg" 
                           id="search-input" 
                           placeholder="输入搜索查询，如：每股收益、earnings per share、分析师预测..."
                           autocomplete="off">
                    <button class="btn btn-primary btn-lg" type="button" id="search-btn">
                        <i class="fas fa-search me-1"></i>
                        搜索
                    </button>
                    <button class="btn btn-outline-secondary btn-lg" type="button" id="clear-filters-btn">
                        <i class="fas fa-times me-1"></i>清除筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="row">
            <div class="col-md-5">
                <div id="results-overview">
                    <div class="card">
                        <div class="card-body text-center text-muted py-5">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <h5>开始搜索</h5>
                            <p>输入关键词开始搜索因子</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-7">
                <div id="result-details">
                    <div class="card">
                        <div class="card-body text-center text-muted py-5">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <h5>选择因子查看详情</h5>
                            <p>点击左侧搜索结果中的任意因子查看详细信息</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 阈值调整按钮 -->
    <div class="position-fixed bottom-0 end-0 p-3">
        <div class="btn-group-vertical" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" id="more-results-btn">
                <i class="fas fa-plus me-1"></i>更多结果
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" id="more-precise-btn">
                <i class="fas fa-crosshairs me-1"></i>更精确
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" id="reset-btn">
                <i class="fas fa-undo me-1"></i>重置
            </button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 简化版应用脚本 -->
    <script src="{{ url_for('static', filename='js/app_simple.js') }}"></script>
</body>
</html>
