<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BERT融合搜索引擎 - 智能动态阈值调整</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-search me-2"></i>
                BERT融合搜索引擎
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="status-indicator">
                    <i class="fas fa-circle text-success me-1"></i>
                    系统就绪
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 数据集选择 -->
        <div class="row mb-3">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            数据集选择
                        </h6>
                    </div>
                    <div class="card-body py-3">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <select class="form-select" id="dataset-select">
                                    <option value="">请选择数据集...</option>
                                    {% for file in data_files %}
                                    <option value="{{ file.filename }}"
                                            {% if file.filename == 'USA_1_TOP3000.csv' %}selected{% endif %}>
                                        {{ file.display_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-info w-100" id="load-dataset-btn">
                                    <i class="fas fa-upload me-1"></i>
                                    加载数据集
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted" id="dataset-info">
                                当前: 美国 - TOP3000 (默认加载)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-brain me-2 text-primary"></i>
                            智能搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 搜索输入 -->
                        <div class="input-group mb-3">
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   id="search-input" 
                                   placeholder="输入搜索查询，如：每股收益、earnings per share、分析师预测..."
                                   autocomplete="off">
                            <button class="btn btn-primary btn-lg" type="button" id="search-btn">
                                <i class="fas fa-search me-1"></i>
                                搜索
                            </button>
                        </div>

                        <!-- 快速查询示例 -->
                        <div class="mb-3">
                            <small class="text-muted">快速查询示例：</small>
                            <div class="mt-1">
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="每股收益">每股收益</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="earnings per share">earnings per share</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="分析师预测">分析师预测</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="EBITDA">EBITDA</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="净资产收益率">净资产收益率</button>
                            </div>
                        </div>

                        <!-- 筛选选项 -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-filter me-1"></i>结果筛选：
                                </small>
                                <button class="btn btn-outline-secondary btn-sm" id="clear-filters-btn">
                                    <i class="fas fa-times me-1"></i>清除筛选
                                </button>
                            </div>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="category-filter">
                                        <option value="">所有主类别</option>
                                        <option value="Fundamental">基础数据 (Fundamental)</option>
                                        <option value="Analyst">分析师数据 (Analyst)</option>
                                        <option value="Price Volume">价格成交量 (Price Volume)</option>
                                        <option value="Model">模型数据 (Model)</option>
                                        <option value="News">新闻数据 (News)</option>
                                        <option value="Option">期权数据 (Option)</option>
                                        <option value="Other">其他数据 (Other)</option>
                                        <option value="Sentiment">情感数据 (Sentiment)</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="subcategory-filter">
                                        <option value="">所有子类别</option>
                                        <option value="Fundamental Data">基础数据</option>
                                        <option value="Analyst Estimates">分析师预测</option>
                                        <option value="Price Volume">价格成交量</option>
                                        <option value="ML/AI Models">机器学习模型</option>
                                        <option value="Estimates Models">预测模型</option>
                                        <option value="Revisions Models">修正模型</option>
                                        <option value="Valuation Models">估值模型</option>
                                        <option value="AI/ML Data">AI/ML数据</option>
                                        <option value="News">新闻</option>
                                        <option value="News Sentiment">新闻情感</option>
                                        <option value="ESG">ESG数据</option>
                                        <option value="Option Volatility">期权波动率</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="dataset-type-filter">
                                        <option value="">所有数据集类型</option>
                                        <option value="Fundamental Point in Time Data">基础时点数据</option>
                                        <option value="Price Volume Data for Equity">股票价量数据</option>
                                        <option value="Company Fundamental Data for Equity">公司基础数据</option>
                                        <option value="Broker Estimates">券商预测</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary btn-sm w-100" id="apply-filters-btn">
                                        <i class="fas fa-filter me-1"></i>应用筛选
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 阈值调整控制 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success" id="more-results-btn">
                                        <i class="fas fa-expand-arrows-alt me-1"></i>
                                        更多结果
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning" id="more-precise-btn">
                                        <i class="fas fa-bullseye me-1"></i>
                                        更精准
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="d-grid">
                                    <button class="btn btn-outline-secondary btn-sm" id="reset-btn">
                                        <i class="fas fa-undo me-1"></i>
                                        重置阈值
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 阈值状态显示 -->
        <div class="row mt-3">
            <div class="col-lg-8 mx-auto">
                <div class="card" id="threshold-status" style="display: none;">
                    <div class="card-body py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted">最小分数</small>
                                <div class="fw-bold" id="min-threshold">0.010</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">语义阈值</small>
                                <div class="fw-bold" id="semantic-threshold">0.300</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">关键词阈值</small>
                                <div class="fw-bold" id="keyword-threshold">0.200</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">用户偏好</small>
                                <div class="fw-bold" id="preference-score">0.50</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="row mt-3">
            <div class="col-lg-8 mx-auto">
                <div class="text-center" id="loading-indicator" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">搜索中...</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">正在使用BERT模型进行智能搜索...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 组合因子功能 -->
        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2 text-success"></i>
                        组合因子生成器
                        <span class="badge bg-success ms-2">新功能</span>
                    </h5>
                    <small class="text-muted">选择两个因子进行相除运算，生成新的组合因子</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 分子因子列表 -->
                        <div class="col-md-5">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-arrow-up me-1"></i>分子因子列表
                                    </h6>
                                    <span class="badge bg-light text-primary" id="numerator-count">0</span>
                                </div>
                                <div class="card-body">
                                    <div id="numerator-list" class="border rounded p-2" style="max-height: 300px; overflow-y: auto;">
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                            <p class="mb-0">从搜索结果中添加分子因子</p>
                                            <small>点击搜索结果详情中的"添加为分子"按钮</small>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <button class="btn btn-outline-primary btn-sm w-100" id="clear-numerator-btn">
                                            <i class="fas fa-trash me-1"></i>清空分子列表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 运算符和批量设置 -->
                        <div class="col-md-2 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <div class="display-1 text-success">÷</div>
                                <small class="text-muted">批量相除</small>
                                <div class="mt-3">
                                    <label class="form-label small">生成数量:</label>
                                    <select class="form-select form-select-sm" id="batch-count-select">
                                        <option value="10">前10个</option>
                                        <option value="20">前20个</option>
                                        <option value="50">前50个</option>
                                        <option value="100">前100个</option>
                                        <option value="200">前200个</option>
                                        <option value="500">前500个</option>
                                        <option value="1000">前1000个</option>
                                        <option value="all" selected>全部</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 分母因子列表 -->
                        <div class="col-md-5">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-arrow-down me-1"></i>分母因子列表
                                    </h6>
                                    <span class="badge bg-light text-warning" id="denominator-count">0</span>
                                </div>
                                <div class="card-body">
                                    <div id="denominator-list" class="border rounded p-2" style="max-height: 300px; overflow-y: auto;">
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                            <p class="mb-0">从搜索结果中添加分母因子</p>
                                            <small>点击搜索结果详情中的"添加为分母"按钮</small>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <button class="btn btn-outline-warning btn-sm w-100" id="clear-denominator-btn">
                                            <i class="fas fa-trash me-1"></i>清空分母列表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量组合生成 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-magic me-1"></i>批量组合生成
                                    </h6>
                                    <span class="badge bg-light text-success" id="combination-count">0</span>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div id="combination-preview">
                                                <div class="text-center text-muted py-4">
                                                    <i class="fas fa-calculator fa-3x mb-3"></i>
                                                    <p>添加分子和分母因子后，可以批量生成组合因子</p>
                                                    <small>每个分子因子将与每个分母因子进行组合</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-success" id="batch-generate-btn" disabled>
                                                    <i class="fas fa-cogs me-1"></i>批量生成
                                                </button>
                                                <button class="btn btn-outline-success" id="export-txt-btn" disabled>
                                                    <i class="fas fa-download me-1"></i>导出TXT
                                                </button>
                                                <button class="btn btn-outline-info" id="preview-combinations-btn" disabled>
                                                    <i class="fas fa-eye me-1"></i>预览组合
                                                </button>
                                                <button class="btn btn-outline-secondary" id="clear-all-btn">
                                                    <i class="fas fa-trash me-1"></i>清空所有
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果 - 两列布局 -->
        <div class="row mt-3">
            <div class="col-lg-5">
                <!-- 左侧：结果总览 -->
                <div id="results-overview"></div>
            </div>
            <div class="col-lg-7">
                <!-- 右侧：详细信息 -->
                <div id="result-details">
                    <div class="card">
                        <div class="card-body text-center text-muted">
                            <i class="fas fa-mouse-pointer mb-3" style="font-size: 3rem;"></i>
                            <h5>点击左侧因子查看详细信息</h5>
                            <p>选择一个因子以查看完整的描述、分类信息和匹配详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索统计 -->
        <div class="row mt-3">
            <div class="col-lg-8 mx-auto">
                <div class="card" id="search-stats" style="display: none;">
                    <div class="card-body py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted">搜索时间</small>
                                <div class="fw-bold" id="search-time">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">结果数量</small>
                                <div class="fw-bold" id="result-count">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">总搜索次数</small>
                                <div class="fw-bold" id="total-searches">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">阈值趋势</small>
                                <div class="fw-bold" id="threshold-trend">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>🚀 核心特性</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-1"></i> BERT多模型融合</li>
                        <li><i class="fas fa-check text-success me-1"></i> 动态阈值调整</li>
                        <li><i class="fas fa-check text-success me-1"></i> 中英文混合搜索</li>
                        <li><i class="fas fa-check text-success me-1"></i> 实时交互优化</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>💡 使用技巧</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 结果太少？点击"更多结果"</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 质量不高？点击"更精准"</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 支持中英文混合查询</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 系统会记住你的偏好</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center text-muted">
                <small>BERT融合搜索引擎 - 基于深度学习的智能检索系统</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
