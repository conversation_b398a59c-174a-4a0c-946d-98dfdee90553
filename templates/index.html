<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BERT融合搜索引擎 - 智能动态阈值调整</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-search me-2"></i>
                BERT融合搜索引擎
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="status-indicator">
                    <i class="fas fa-circle text-success me-1"></i>
                    系统就绪
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 数据集选择 -->
        <div class="row mb-3">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            数据集选择
                        </h6>
                    </div>
                    <div class="card-body py-3">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <select class="form-select" id="dataset-select">
                                    <option value="">请选择数据集...</option>
                                    {% for file in data_files %}
                                    <option value="{{ file.filename }}"
                                            {% if file.filename == 'USA_1_TOP3000.csv' %}selected{% endif %}>
                                        {{ file.display_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-info w-100" id="load-dataset-btn">
                                    <i class="fas fa-upload me-1"></i>
                                    加载数据集
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted" id="dataset-info">
                                当前: 美国 - TOP3000 (默认加载)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-brain me-2 text-primary"></i>
                            智能搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 类型筛选 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label small text-muted">主类别</label>
                                <select class="form-select" id="category-select">
                                    <option value="">所有主类别</option>
                                    <option value="Analyst">分析师 (Analyst)</option>
                                    <option value="Fundamental">基本面 (Fundamental)</option>
                                    <option value="Model">模型 (Model)</option>
                                    <option value="News">新闻 (News)</option>
                                    <option value="Option">期权 (Option)</option>
                                    <option value="Price Volume">价量 (Price Volume)</option>
                                    <option value="Risk">风险 (Risk)</option>
                                    <option value="Sentiment">情绪 (Sentiment)</option>
                                    <option value="Short Interest">做空 (Short Interest)</option>
                                    <option value="Social Media">社交媒体 (Social Media)</option>
                                    <option value="Other">其他 (Other)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label small text-muted">子类别</label>
                                <select class="form-select" id="subcategory-select">
                                    <option value="">所有子类别</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label small text-muted">数据源</label>
                                <select class="form-select" id="dataset-name-select">
                                    <option value="">所有数据源</option>
                                </select>
                            </div>
                        </div>

                        <!-- 搜索输入 -->
                        <div class="input-group mb-3">
                            <input type="text"
                                   class="form-control form-control-lg"
                                   id="search-input"
                                   placeholder="输入搜索查询，如：每股收益、earnings per share、分析师预测..."
                                   autocomplete="off">
                            <button class="btn btn-primary btn-lg" type="button" id="search-btn">
                                <i class="fas fa-search me-1"></i>
                                搜索
                            </button>
                            <button class="btn btn-outline-secondary btn-lg" type="button" id="clear-filters-btn">
                                <i class="fas fa-times me-1"></i>清除筛选
                            </button>
                        </div>

                        <!-- 快速查询示例 -->
                        <div class="mb-3">
                            <small class="text-muted">快速查询示例：</small>
                            <div class="mt-1">
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="每股收益">每股收益</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="earnings per share">earnings per share</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="分析师预测">分析师预测</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="EBITDA">EBITDA</button>
                                <button class="btn btn-outline-secondary btn-sm me-1 mb-1 quick-query" data-query="净资产收益率">净资产收益率</button>
                            </div>
                        </div>

                        <!-- 阈值调整控制 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success" id="more-results-btn">
                                        <i class="fas fa-expand-arrows-alt me-1"></i>
                                        更多结果
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning" id="more-precise-btn">
                                        <i class="fas fa-bullseye me-1"></i>
                                        更精准
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="d-grid">
                                    <button class="btn btn-outline-secondary btn-sm" id="reset-btn">
                                        <i class="fas fa-undo me-1"></i>
                                        重置阈值
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 阈值状态显示 -->
        <div class="row mt-3">
            <div class="col-lg-8 mx-auto">
                <div class="card" id="threshold-status" style="display: none;">
                    <div class="card-body py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted">最小分数</small>
                                <div class="fw-bold" id="min-threshold">0.010</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">语义阈值</small>
                                <div class="fw-bold" id="semantic-threshold">0.300</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">关键词阈值</small>
                                <div class="fw-bold" id="keyword-threshold">0.200</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">用户偏好</small>
                                <div class="fw-bold" id="preference-score">0.50</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="row mt-3">
            <div class="col-lg-8 mx-auto">
                <div class="text-center" id="loading-indicator" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">搜索中...</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">正在使用BERT模型进行智能搜索...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果 - 两列布局 -->
        <div class="row mt-3">
            <div class="col-lg-5">
                <!-- 左侧：结果总览 -->
                <div id="results-overview"></div>
            </div>
            <div class="col-lg-7">
                <!-- 右侧：详细信息 -->
                <div id="result-details">
                    <div class="card">
                        <div class="card-body text-center text-muted">
                            <i class="fas fa-mouse-pointer mb-3" style="font-size: 3rem;"></i>
                            <h5>点击左侧因子查看详细信息</h5>
                            <p>选择一个因子以查看完整的描述、分类信息和匹配详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索统计 -->
        <div class="row mt-3">
            <div class="col-lg-8 mx-auto">
                <div class="card" id="search-stats" style="display: none;">
                    <div class="card-body py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted">搜索时间</small>
                                <div class="fw-bold" id="search-time">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">结果数量</small>
                                <div class="fw-bold" id="result-count">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">总搜索次数</small>
                                <div class="fw-bold" id="total-searches">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">阈值趋势</small>
                                <div class="fw-bold" id="threshold-trend">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>🚀 核心特性</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-1"></i> BERT多模型融合</li>
                        <li><i class="fas fa-check text-success me-1"></i> 动态阈值调整</li>
                        <li><i class="fas fa-check text-success me-1"></i> 中英文混合搜索</li>
                        <li><i class="fas fa-check text-success me-1"></i> 实时交互优化</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>💡 使用技巧</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 结果太少？点击"更多结果"</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 质量不高？点击"更精准"</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 支持中英文混合查询</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> 系统会记住你的偏好</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center text-muted">
                <small>BERT融合搜索引擎 - 基于深度学习的智能检索系统</small>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
