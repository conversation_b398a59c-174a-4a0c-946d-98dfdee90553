#!/usr/bin/env python3
"""
改进的搜索引擎
修复结果多样性问题，集成专业翻译，优化排序算法
"""

import time
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import os
import pickle
import threading
from pathlib import Path
import random
from collections import defaultdict

# 导入优化的模型管理器
from optimized_model_manager import OptimizedModelManager, CacheConfig
from single_bert_fusion_engine import SearchResult, EnhancedChineseNLP
from dynamic_threshold_manager import (
    DynamicThresholdManager, SearchContext, SearchMetrics
)
from sklearn.metrics.pairwise import cosine_similarity

# 导入简化翻译功能
from simple_translator import SimpleTranslator

class ImprovedSearchEngine:
    """改进的搜索引擎"""
    
    def __init__(self, cache_config: CacheConfig = None):
        """初始化改进搜索引擎"""
        print("🚀 Initializing Improved Search Engine...")
        
        # 配置
        self.cache_config = cache_config or CacheConfig()
        
        # 优化的模型管理器
        self.model_manager = OptimizedModelManager(self.cache_config)
        
        # 阈值管理器
        self.threshold_manager = DynamicThresholdManager()
        
        # 简化翻译器
        self.translator = SimpleTranslator()
        
        # 数据存储
        self.df = None
        self.indexed_documents = {}
        self.data_hash = None
        
        # 用户会话管理
        self.user_sessions = {}
        
        # 初始化状态
        self.initialization_status = {
            "status": "not_started", 
            "progress": 0, 
            "message": "",
            "current_step": "",
            "total_steps": 0,
            "completed_steps": 0
        }
        
        # 列权重
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
        
        # 当前搜索阈值（更宽松的默认值）
        self.current_thresholds = {
            'min_score': 0.005,  # 降低最小分数阈值
            'semantic': 0.15,    # 降低语义阈值
            'keyword': 0.1       # 降低关键词阈值
        }
        
        print("✅ Improved Search Engine initialized")
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """获取初始化状态"""
        return self.initialization_status.copy()
    
    def preload_models_fast(self, model_configs: Dict[str, str] = None):
        """快速预加载模型"""
        self.initialization_status.update({
            "status": "initializing",
            "progress": 10,
            "message": "预加载BERT模型...",
            "current_step": "模型加载"
        })
        
        print("📥 Fast preloading models...")
        self.model_manager.preload_models(model_configs)
        
        self.initialization_status.update({
            "progress": 30,
            "message": "模型预加载完成",
        })
        
        print("✅ Models preloaded successfully")
    
    def load_data_and_build_index_fast(self, file_path: str, 
                                      sample_size: int = None,
                                      batch_size: int = 100):
        """快速加载数据并构建索引"""
        print(f"📊 Fast loading data from {file_path}...")
        
        # 加载数据
        self.df = pd.read_csv(file_path)
        
        # 如果指定了样本大小，使用分层采样而不是简单的head()
        if sample_size and sample_size < len(self.df):
            print(f"🎯 Using stratified sample of {sample_size} records")
            
            # 按首字母分层采样，确保多样性
            grouped = self.df.groupby(self.df['id'].str[0])
            sample_dfs = []
            
            for letter, group in grouped:
                # 每个字母组按比例采样
                group_sample_size = max(1, int(len(group) * sample_size / len(self.df)))
                if len(group) <= group_sample_size:
                    sample_dfs.append(group)
                else:
                    sample_dfs.append(group.sample(n=group_sample_size, random_state=42))
            
            self.df = pd.concat(sample_dfs, ignore_index=True)
            print(f"📈 Stratified sampling result: {len(self.df)} records")
        
        self.data_hash = self.model_manager._get_data_hash(self.df)
        
        self.initialization_status.update({
            "progress": 40,
            "message": f"已加载 {len(self.df)} 条记录",
            "current_step": "数据加载"
        })
        
        print(f"✅ Loaded {len(self.df)} records")
        print(f"🔑 Data hash: {self.data_hash[:8]}...")
        
        # 尝试加载缓存的索引
        cached_index = self.model_manager.load_index(self.data_hash)
        if cached_index:
            print("📦 Loading index from cache...")
            self.indexed_documents = cached_index
            
            self.initialization_status.update({
                "status": "completed",
                "progress": 100,
                "message": f"索引已从缓存加载 ({len(self.indexed_documents)} 文档)",
                "current_step": "完成"
            })
            
            print(f"✅ Index loaded from cache ({len(self.indexed_documents)} documents)")
            return
        
        # 构建新索引（分批处理）
        print("🔨 Building new search index with batch processing...")
        self._build_search_index_batched(batch_size)
        
        # 保存索引到缓存
        self.model_manager.save_index(self.indexed_documents, self.data_hash)
        
        self.initialization_status.update({
            "status": "completed",
            "progress": 100,
            "message": f"搜索索引构建完成 ({len(self.indexed_documents)} 文档)",
            "current_step": "完成"
        })
        
        print("✅ Search index built and cached successfully")
    
    def _build_search_index_batched(self, batch_size: int = 100):
        """分批构建搜索索引"""
        start_time = time.time()
        
        target_columns = list(self.column_weights.keys())
        total_rows = len(self.df)
        
        self.initialization_status.update({
            "progress": 50,
            "message": "开始构建搜索索引...",
            "current_step": "索引构建",
            "total_steps": total_rows,
            "completed_steps": 0
        })
        
        print(f"📝 Building index for {total_rows} documents in batches of {batch_size}...")
        
        # 分批处理文档
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_df = self.df.iloc[batch_start:batch_end]
            
            print(f"   Processing batch {batch_start//batch_size + 1}/{(total_rows-1)//batch_size + 1} "
                  f"(rows {batch_start}-{batch_end-1})")
            
            # 收集当前批次的所有文本
            batch_texts = []
            batch_text_to_doc_col = {}
            
            for idx, row in batch_df.iterrows():
                for col_name in target_columns:
                    if col_name in row and pd.notna(row[col_name]):
                        text_content = str(row[col_name])
                        if text_content not in batch_text_to_doc_col:
                            batch_texts.append(text_content)
                            batch_text_to_doc_col[text_content] = []
                        batch_text_to_doc_col[text_content].append((idx, col_name))
            
            # 批量编码当前批次的文本
            if batch_texts:
                print(f"     Encoding {len(batch_texts)} unique texts...")
                batch_vectors = self.model_manager.batch_encode_texts(batch_texts)
                text_to_vector = dict(zip(batch_texts, batch_vectors))
            
            # 构建当前批次的文档索引
            for idx, row in batch_df.iterrows():
                column_features = {}
                
                for col_name in target_columns:
                    if col_name in row and pd.notna(row[col_name]):
                        text_content = str(row[col_name])
                        
                        vector = text_to_vector.get(text_content)
                        if vector is None:
                            continue
                        
                        # NLP分析
                        keywords = self.model_manager.chinese_nlp.extract_keywords(text_content, top_k=15)
                        entities = self.model_manager.chinese_nlp.extract_entities(text_content)
                        
                        column_features[col_name] = {
                            'text': text_content,
                            'keywords': keywords,
                            'entities': entities,
                            'vector': vector
                        }
                
                # 创建索引文档
                if column_features:
                    self.indexed_documents[idx] = {
                        'doc_id': idx,
                        'column_features': column_features,
                        'row_data': row.to_dict()
                    }
            
            # 更新进度
            completed_steps = batch_end
            progress = 50 + int((completed_steps / total_rows) * 40)
            
            self.initialization_status.update({
                "progress": progress,
                "message": f"已处理 {completed_steps}/{total_rows} 文档",
                "completed_steps": completed_steps
            })
        
        build_time = time.time() - start_time
        print(f"✅ Search index built in {build_time:.2f}s")
        
        self.initialization_status.update({
            "progress": 95,
            "message": "正在保存索引到缓存...",
            "current_step": "保存缓存"
        })
    
    def adjust_thresholds(self, action: str, session_id: str = "default"):
        """调整搜索阈值"""
        if session_id not in self.user_sessions:
            self.user_sessions[session_id] = {
                'thresholds': self.current_thresholds.copy(),
                'preference_score': 0.5,
                'last_query': '',
                'search_count': 0
            }
        
        session = self.user_sessions[session_id]
        adjustment_step = 0.03  # 减小调整步长
        
        if action == "more_results":
            session['thresholds']['min_score'] = max(0.001, session['thresholds']['min_score'] - adjustment_step)
            session['thresholds']['semantic'] = max(0.05, session['thresholds']['semantic'] - adjustment_step)
            session['thresholds']['keyword'] = max(0.05, session['thresholds']['keyword'] - adjustment_step)
            session['preference_score'] = max(0.0, session['preference_score'] - 0.1)
            print(f"📉 降低阈值: min={session['thresholds']['min_score']:.3f}, semantic={session['thresholds']['semantic']:.3f}")
            
        elif action == "more_precise":
            session['thresholds']['min_score'] = min(0.3, session['thresholds']['min_score'] + adjustment_step)
            session['thresholds']['semantic'] = min(0.7, session['thresholds']['semantic'] + adjustment_step)
            session['thresholds']['keyword'] = min(0.7, session['thresholds']['keyword'] + adjustment_step)
            session['preference_score'] = min(1.0, session['preference_score'] + 0.1)
            print(f"📈 提高阈值: min={session['thresholds']['min_score']:.3f}, semantic={session['thresholds']['semantic']:.3f}")
            
        elif action == "reset":
            session['thresholds'] = self.current_thresholds.copy()
            session['preference_score'] = 0.5
            print(f"🔄 重置阈值到默认值")
        
        return session['thresholds']
    
    def get_session_stats(self, session_id: str = "default") -> Dict[str, Any]:
        """获取会话统计"""
        if session_id not in self.user_sessions:
            return {
                'current_thresholds': self.current_thresholds,
                'preference_score': 0.5,
                'threshold_trend': 'stable',
                'total_searches': 0
            }
        
        session = self.user_sessions[session_id]
        return {
            'current_thresholds': session['thresholds'],
            'preference_score': session['preference_score'],
            'threshold_trend': 'stable',
            'total_searches': session['search_count']
        }

    def search(self, query: str, top_k: int = None,
               search_context: SearchContext = SearchContext.INTERACTIVE,
               session_id: str = "default") -> List[SearchResult]:
        """执行搜索（改进版，支持多样性排序）"""
        if not self.indexed_documents:
            raise ValueError("Search index not built. Please initialize first.")

        print(f"🔍 Searching for: '{query}'")
        start_time = time.time()

        # 获取用户会话阈值
        if session_id not in self.user_sessions:
            self.user_sessions[session_id] = {
                'thresholds': self.current_thresholds.copy(),
                'preference_score': 0.5,
                'last_query': query,
                'search_count': 0
            }

        session = self.user_sessions[session_id]
        session['last_query'] = query
        session['search_count'] += 1

        # 使用会话阈值
        user_thresholds = session['thresholds']
        query_type = self.threshold_manager.classify_query_type(query)

        print(f"🎛️ 使用用户阈值: min={user_thresholds['min_score']:.3f}, semantic={user_thresholds['semantic']:.3f}")

        # 查询预处理
        query_keywords = self.model_manager.chinese_nlp.extract_keywords(query, top_k=15)

        # 编码查询（使用缓存）
        query_vector = self.model_manager.encode_text_cached(query)

        # 搜索所有文档
        search_results = []

        for doc_idx, indexed_doc in self.indexed_documents.items():
            column_features = indexed_doc['column_features']
            row_data = indexed_doc['row_data']

            # 计算分数
            overall_semantic_score = 0.0
            overall_keyword_score = 0.0
            overall_score = 0.0
            matched_columns = []
            matched_keywords = []

            for col_name, col_feature in column_features.items():
                # 关键词匹配分数
                keyword_score = self._calculate_keyword_similarity(
                    query_keywords, col_feature['keywords']
                )

                # 语义相似度分数
                semantic_score = self._calculate_semantic_similarity(
                    query_vector, col_feature['vector']
                )

                # 综合列分数
                col_score = keyword_score * 0.4 + semantic_score * 0.6

                # 累积分数
                col_weight = self.column_weights[col_name]
                overall_semantic_score += semantic_score * col_weight
                overall_keyword_score += keyword_score * col_weight
                overall_score += col_score * col_weight

                # 记录匹配信息
                if col_score > user_thresholds['min_score']:
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw, weight in col_feature['keywords'][:5]])

            # 创建搜索结果
            if (overall_score > user_thresholds['min_score'] and
                (overall_semantic_score > user_thresholds['semantic'] or
                 overall_keyword_score > user_thresholds['keyword'])):

                result = SearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),

                    overall_score=overall_score,
                    semantic_score=overall_semantic_score,
                    keyword_score=overall_keyword_score,
                    column_scores={},

                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='improved_bert_fusion'
                )

                search_results.append(result)

        # 排序结果
        if top_k and len(search_results) > top_k:
            # 如果指定了top_k且结果太多，使用多样性排序
            final_results = self._diversified_ranking(search_results, top_k)
        else:
            # 否则返回所有结果，按分数排序
            final_results = sorted(search_results, key=lambda x: x.overall_score, reverse=True)

        search_time = time.time() - start_time
        print(f"✅ Search completed in {search_time:.3f}s, found {len(final_results)} results")

        return final_results

    def _diversified_ranking(self, results: List[SearchResult], top_k: int) -> List[SearchResult]:
        """多样性排序算法"""
        if len(results) <= top_k:
            return sorted(results, key=lambda x: x.overall_score, reverse=True)

        print(f"🎯 Applying diversified ranking to {len(results)} results...")

        # 按分数排序
        results.sort(key=lambda x: x.overall_score, reverse=True)

        # 分组：按ID首字母分组
        groups = defaultdict(list)
        for result in results:
            first_letter = result.id[0].lower() if result.id else 'other'
            groups[first_letter].append(result)

        # 多样性选择
        selected_results = []
        group_keys = list(groups.keys())

        # 轮询选择，确保多样性
        round_robin_index = 0
        while len(selected_results) < top_k and any(groups.values()):
            current_group = group_keys[round_robin_index % len(group_keys)]

            if groups[current_group]:
                # 从当前组选择最高分的结果
                best_result = groups[current_group].pop(0)
                selected_results.append(best_result)

            round_robin_index += 1

            # 如果所有组都空了，跳出循环
            if all(not group for group in groups.values()):
                break

        print(f"📊 Diversified ranking: selected {len(selected_results)} results from {len(group_keys)} groups")

        # 按分数重新排序最终结果
        selected_results.sort(key=lambda x: x.overall_score, reverse=True)

        return selected_results[:top_k]

    def _calculate_semantic_similarity(self, query_vector: np.ndarray,
                                     doc_vector: np.ndarray) -> float:
        """计算语义相似度"""
        try:
            similarity = cosine_similarity(
                query_vector.reshape(1, -1),
                doc_vector.reshape(1, -1)
            )[0][0]
            return float(similarity)
        except:
            return 0.0

    def _calculate_keyword_similarity(self, query_keywords: List[Tuple[str, float]],
                                    doc_keywords: List[Tuple[str, float]]) -> float:
        """计算关键词相似度"""
        if not query_keywords or not doc_keywords:
            return 0.0

        query_dict = {word.lower(): weight for word, weight in query_keywords}
        doc_dict = {word.lower(): weight for word, weight in doc_keywords}

        score = 0.0
        total_query_weight = sum(query_dict.values())

        for word, weight in query_dict.items():
            if word in doc_dict:
                score += min(weight, doc_dict[word]) * 2.0
            else:
                # 部分匹配
                for doc_word in doc_dict:
                    if word in doc_word or doc_word in word:
                        score += weight * 0.8
                        break

        return score / (total_query_weight + 1e-8)

    def translate_result(self, result: SearchResult) -> Dict[str, str]:
        """翻译搜索结果"""
        return {
            'id': result.id,  # ID不翻译
            'description_translated': self.translator.translate_to_chinese(result.description, 'description'),
            'dataset_name_translated': self.translator.translate_to_chinese(result.dataset_name, 'dataset'),
            'category_name_translated': self.translator.translate_to_chinese(result.category_name, 'category'),
            'subcategory_name_translated': self.translator.translate_to_chinese(result.subcategory_name, 'subcategory'),
            'region_translated': self.translator.translate_to_chinese(result.region, 'region'),
            'delay_translated': self.translator.translate_to_chinese(result.delay, 'delay')
        }

if __name__ == "__main__":
    # 测试改进的搜索引擎
    print("🧪 Testing Improved Search Engine")
    print("=" * 60)

    # 初始化引擎
    engine = ImprovedSearchEngine()

    # 预加载模型
    engine.preload_models_fast()

    # 快速加载数据（使用分层采样）
    engine.load_data_and_build_index_fast(
        "split_files/USA_1_TOP3000.csv",
        sample_size=2000,
        batch_size=200
    )

    # 测试搜索
    test_queries = [
        "每股收益",
        "earnings per share",
        "分析师预测"
    ]

    print(f"\n🔍 Testing diversified search...")

    for query in test_queries:
        print(f"\n📝 Query: '{query}'")

        start_time = time.time()
        results = engine.search(query, top_k=20)
        search_time = time.time() - start_time

        print(f"   ⏱️  Search time: {search_time:.3f}s")
        print(f"   📊 Results: {len(results)}")

        # 显示ID首字母分布
        first_letters = [r.id[0].lower() for r in results if r.id]
        letter_counts = {}
        for letter in first_letters:
            letter_counts[letter] = letter_counts.get(letter, 0) + 1

        print(f"   🎯 ID diversity: {dict(sorted(letter_counts.items()))}")

        for i, result in enumerate(results[:5], 1):
            print(f"   {i}. {result.id} (Score: {result.overall_score:.3f})")

    print(f"\n🎉 Improved search test completed!")
