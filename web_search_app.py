#!/usr/bin/env python3
"""
BERT融合搜索引擎 Web应用
集成动态阈值调整的交互式搜索界面
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import pandas as pd
import numpy as np
import time
import uuid
import json
from typing import Dict, List, Any
import os

# 导入我们的搜索引擎
from interactive_search_engine import InteractiveSearchEngine
from dynamic_threshold_manager import SearchContext

app = Flask(__name__)
app.secret_key = 'bert_fusion_search_engine_2024'
CORS(app)

# 全局搜索引擎实例
search_engine = None
data_loaded = False

def initialize_search_engine():
    """初始化搜索引擎"""
    global search_engine, data_loaded
    
    if search_engine is None:
        print("🚀 Initializing BERT Fusion Search Engine...")
        search_engine = InteractiveSearchEngine()
        
        # 加载数据
        data_file = "split_files/USA_1_TOP3000.csv"
        if os.path.exists(data_file):
            print(f"📊 Loading data from {data_file}...")
            search_engine.load_data(data_file)
            data_loaded = True
            print("✅ Search engine initialized successfully!")
        else:
            print(f"❌ Data file not found: {data_file}")
            data_loaded = False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/search', methods=['POST'])
def api_search():
    """搜索API"""
    try:
        if not data_loaded:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized. Please check data file.'
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'Query cannot be empty'
            })
        
        # 获取或创建会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id
        
        # 执行搜索
        start_time = time.time()
        results = search_engine.handle_interaction(query, session_id)
        search_time = time.time() - start_time
        
        # 获取会话统计
        stats = search_engine.get_session_stats(session_id)
        
        # 格式化结果
        formatted_results = []
        for result in results:
            formatted_results.append({
                'id': result.id,
                'description': result.description,
                'dataset_name': result.dataset_name,
                'category_name': result.category_name,
                'subcategory_name': result.subcategory_name,
                'region': result.region,
                'universe': result.universe,
                'delay': result.delay,
                'overall_score': round(result.overall_score, 4),
                'semantic_score': round(result.semantic_score, 4),
                'keyword_score': round(result.keyword_score, 4),
                'matched_columns': result.matched_columns,
                'matched_keywords': result.matched_keywords[:10]  # 限制关键词数量
            })
        
        return jsonify({
            'success': True,
            'results': formatted_results,
            'search_time': round(search_time, 3),
            'total_results': len(formatted_results),
            'session_stats': {
                'current_thresholds': stats.get('current_thresholds', {}),
                'preference_score': round(stats.get('preference_score', 0.5), 2),
                'threshold_trend': stats.get('threshold_trend', 'stable'),
                'total_searches': stats.get('total_searches', 0)
            }
        })
        
    except Exception as e:
        print(f"❌ Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/adjust_threshold', methods=['POST'])
def api_adjust_threshold():
    """阈值调整API"""
    try:
        if not data_loaded:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })
        
        data = request.get_json()
        action = data.get('action', '').strip().lower()
        
        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id
        
        # 映射动作到指令
        action_mapping = {
            'more_results': '更多结果',
            'more_precise': '更精准',
            'reset': '重置'
        }
        
        if action not in action_mapping:
            return jsonify({
                'success': False,
                'error': f'Invalid action: {action}'
            })
        
        # 执行阈值调整
        command = action_mapping[action]
        results = search_engine.handle_interaction(command, session_id)
        
        # 获取更新后的统计信息
        stats = search_engine.get_session_stats(session_id)
        
        # 格式化结果（如果有重新搜索）
        formatted_results = []
        if results:
            for result in results:
                formatted_results.append({
                    'id': result.id,
                    'description': result.description,
                    'dataset_name': result.dataset_name,
                    'category_name': result.category_name,
                    'subcategory_name': result.subcategory_name,
                    'overall_score': round(result.overall_score, 4),
                    'semantic_score': round(result.semantic_score, 4),
                    'keyword_score': round(result.keyword_score, 4),
                    'matched_columns': result.matched_columns,
                    'matched_keywords': result.matched_keywords[:10]
                })
        
        return jsonify({
            'success': True,
            'action': action,
            'results': formatted_results,
            'session_stats': {
                'current_thresholds': stats.get('current_thresholds', {}),
                'preference_score': round(stats.get('preference_score', 0.5), 2),
                'threshold_trend': stats.get('threshold_trend', 'stable'),
                'total_searches': stats.get('total_searches', 0)
            }
        })
        
    except Exception as e:
        print(f"❌ Threshold adjustment error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/status')
def api_status():
    """系统状态API"""
    return jsonify({
        'success': True,
        'data_loaded': data_loaded,
        'engine_ready': search_engine is not None,
        'session_id': session.get('session_id'),
        'timestamp': time.time()
    })

@app.route('/api/stats')
def api_stats():
    """获取详细统计信息"""
    try:
        session_id = session.get('session_id')
        if not session_id or not search_engine:
            return jsonify({
                'success': False,
                'error': 'No active session'
            })
        
        stats = search_engine.get_session_stats(session_id)
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    # 初始化搜索引擎
    initialize_search_engine()
    
    # 启动Flask应用
    print("🌐 Starting BERT Fusion Search Web App...")
    print("📱 Access the app at: http://localhost:5000")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
